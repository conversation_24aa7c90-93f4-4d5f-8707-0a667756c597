"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/users/page",{

/***/ "(app-pages-browser)/./src/components/admin/preferences/NotificationPreferences.js":
/*!*********************************************************************!*\
  !*** ./src/components/admin/preferences/NotificationPreferences.js ***!
  \*********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationPreferences)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.js\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.js\");\n/* harmony import */ var _ui_ModuleCheckbox__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../ui/ModuleCheckbox */ \"(app-pages-browser)/./src/components/ui/ModuleCheckbox.js\");\n/* harmony import */ var _PreferencesSection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./PreferencesSection */ \"(app-pages-browser)/./src/components/admin/preferences/PreferencesSection.js\");\n/* harmony import */ var _services_preferencesService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/services/preferencesService */ \"(app-pages-browser)/./src/services/preferencesService.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-check.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _AppointmentTimingSettings__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./AppointmentTimingSettings */ \"(app-pages-browser)/./src/components/admin/preferences/AppointmentTimingSettings.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Definir tipos de notificação organizados por categoria\nconst NOTIFICATION_CATEGORIES = {\n    appointments: {\n        title: \"Consultas\",\n        description: \"Notificações relacionadas a agendamentos e consultas\",\n        moduleColor: \"scheduler\",\n        notifications: [\n            {\n                key: \"APPOINTMENT_COMING\",\n                label: \"Consultas Chegando\",\n                description: \"Avisos de consultas agendadas (1 hora antes)\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 22,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"NEW_APPOINTMENT_SCHEDULED\",\n                label: \"Nova Consulta Agendada\",\n                description: \"Notificações quando uma nova consulta é agendada no sistema\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 28,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"APPOINTMENT_SCHEDULED_FOR_PROVIDER\",\n                label: \"Consulta Agendada (Profissional)\",\n                description: \"Notificar profissionais quando consultas são agendadas para eles\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 34,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"APPOINTMENT_SCHEDULED_FOR_PATIENT\",\n                label: \"Consulta Agendada (Paciente)\",\n                description: \"Notificar pacientes/clientes quando consultas são agendadas\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 40,\n                    columnNumber: 15\n                }, undefined)\n            }\n        ]\n    },\n    requests: {\n        title: \"Solicitações\",\n        description: \"Notificações sobre solicitações de agendamento\",\n        moduleColor: \"scheduler\",\n        notifications: [\n            {\n                key: \"NEW_APPOINTMENT_REQUEST\",\n                label: \"Nova Solicitação de Agendamento\",\n                description: \"Notificações quando clientes criam novas solicitações de agendamento\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 53,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"APPOINTMENT_REQUEST_APPROVED\",\n                label: \"Solicitação Aprovada\",\n                description: \"Notificar clientes quando suas solicitações são aprovadas\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 59,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"APPOINTMENT_REQUEST_SUGGESTED\",\n                label: \"Sugestão de Alteração\",\n                description: \"Notificar clientes quando há sugestões de alteração em suas solicitações\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 65,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"APPOINTMENT_REQUEST_REJECTED\",\n                label: \"Solicitação Rejeitada\",\n                description: \"Notificar clientes quando suas solicitações são rejeitadas\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 71,\n                    columnNumber: 15\n                }, undefined)\n            }\n        ]\n    },\n    registrations: {\n        title: \"Novos Registros\",\n        description: \"Notificações sobre novos cadastros e acessos\",\n        moduleColor: \"admin\",\n        notifications: [\n            {\n                key: \"NEW_REGISTRATION\",\n                label: \"Novos Cadastros\",\n                description: \"Notificações sobre novos cadastros no sistema\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 84,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"NEW_ACCESS\",\n                label: \"Novos Acessos\",\n                description: \"Notificações sobre novos acessos ao sistema\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 90,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"NEW_BACKUP\",\n                label: \"Novo Backup\",\n                description: \"Confirmação de backups realizados\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 96,\n                    columnNumber: 15\n                }, undefined)\n            }\n        ]\n    },\n    documents: {\n        title: \"Documentos/Exportações\",\n        description: \"Notificações sobre documentos e exportações\",\n        moduleColor: \"people\",\n        notifications: [\n            {\n                key: \"DOCUMENT_SHARED\",\n                label: \"Documentos Compartilhados\",\n                description: \"Notificações quando documentos são compartilhados com usuários ou clientes\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 109,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"NEW_EXPORT\",\n                label: \"Exportações\",\n                description: \"Notificações sobre exportações concluídas\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 115,\n                    columnNumber: 15\n                }, undefined)\n            }\n        ]\n    }\n};\n// Função para obter todas as notificações em formato plano (para compatibilidade)\nconst getAllNotifications = ()=>{\n    const allNotifications = [];\n    Object.values(NOTIFICATION_CATEGORIES).forEach((category)=>{\n        allNotifications.push(...category.notifications);\n    });\n    return allNotifications;\n};\nconst NOTIFICATION_TYPES = getAllNotifications();\n// Função para obter as cores do ícone baseado no módulo\nconst getIconColors = (moduleColor)=>{\n    const colorMap = {\n        scheduler: {\n            text: \"text-purple-600 dark:text-purple-400\"\n        },\n        people: {\n            text: \"text-orange-600 dark:text-orange-400\"\n        },\n        admin: {\n            text: \"text-gray-600 dark:text-gray-400\"\n        },\n        default: {\n            text: \"text-cyan-600 dark:text-cyan-400\"\n        }\n    };\n    return colorMap[moduleColor] || colorMap.default;\n};\nfunction NotificationPreferences(param) {\n    let { search = \"\", searchMode = false, preferences = null, selectedCompanyId = null, onSave = null } = param;\n    _s();\n    // Estados para preferências de notificação\n    const [enabledNotifications, setEnabledNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [requiredNotifications, setRequiredNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [appointmentTimings, setAppointmentTimings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    // Carregar preferências quando o componente receber os dados\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationPreferences.useEffect\": ()=>{\n            if (preferences && preferences.notifications) {\n                setEnabledNotifications(preferences.notifications.enabled || {});\n                setRequiredNotifications(preferences.notifications.required || {});\n                setAppointmentTimings(preferences.notifications.timings || {});\n            } else {\n                // Definir valores padrão se não houver preferências\n                const defaultEnabled = {};\n                const defaultRequired = {};\n                const defaultTimings = {};\n                NOTIFICATION_TYPES.forEach({\n                    \"NotificationPreferences.useEffect\": (type)=>{\n                        defaultEnabled[type.key] = false;\n                        defaultRequired[type.key] = false;\n                        // Valor padrão para APPOINTMENT_COMING: 1 hora antes\n                        if (type.key === 'APPOINTMENT_COMING') {\n                            defaultTimings[type.key] = [\n                                {\n                                    value: 1,\n                                    unit: 'hours'\n                                }\n                            ];\n                        }\n                    }\n                }[\"NotificationPreferences.useEffect\"]);\n                setEnabledNotifications(defaultEnabled);\n                setRequiredNotifications(defaultRequired);\n                setAppointmentTimings(defaultTimings);\n            }\n        }\n    }[\"NotificationPreferences.useEffect\"], [\n        preferences\n    ]);\n    // Filtrar categorias baseado na pesquisa\n    const filteredCategories = searchMode && search ? Object.entries(NOTIFICATION_CATEGORIES).reduce((acc, param)=>{\n        let [key, category] = param;\n        const filteredNotifications = category.notifications.filter((notification)=>notification.label.toLowerCase().includes(search.toLowerCase()) || notification.description.toLowerCase().includes(search.toLowerCase()));\n        if (filteredNotifications.length > 0) {\n            acc[key] = {\n                ...category,\n                notifications: filteredNotifications\n            };\n        }\n        return acc;\n    }, {}) : NOTIFICATION_CATEGORIES;\n    // Manter compatibilidade com filteredNotifications para o resto do código\n    const filteredNotifications = searchMode && search ? NOTIFICATION_TYPES.filter((notification)=>notification.label.toLowerCase().includes(search.toLowerCase()) || notification.description.toLowerCase().includes(search.toLowerCase())) : NOTIFICATION_TYPES;\n    // Se não há resultados na pesquisa, não mostrar nada\n    if (searchMode && Object.keys(filteredCategories).length === 0) {\n        return null;\n    }\n    // Função para toggle de notificação habilitada\n    const toggleNotificationEnabled = (key)=>{\n        setEnabledNotifications((prev)=>({\n                ...prev,\n                [key]: !prev[key]\n            }));\n        // Se desabilitar, também desabilitar obrigatoriedade\n        if (enabledNotifications[key]) {\n            setRequiredNotifications((prev)=>({\n                    ...prev,\n                    [key]: false\n                }));\n        }\n    };\n    // Função para toggle de obrigatoriedade\n    const toggleNotificationRequired = (key)=>{\n        setRequiredNotifications((prev)=>({\n                ...prev,\n                [key]: !prev[key]\n            }));\n    };\n    // Função para atualizar timings de uma notificação específica\n    const handleTimingChange = (notificationKey, timings)=>{\n        setAppointmentTimings((prev)=>({\n                ...prev,\n                [notificationKey]: timings\n            }));\n    };\n    // Função para salvar preferências\n    const handleSave = async ()=>{\n        setIsSaving(true);\n        try {\n            const notificationPreferences = {\n                enabled: enabledNotifications,\n                required: requiredNotifications,\n                timings: appointmentTimings\n            };\n            // Manter as preferências existentes e adicionar as de notificação\n            const currentPreferences = preferences || {};\n            const updatedPreferences = {\n                ...currentPreferences,\n                notifications: notificationPreferences\n            };\n            if (onSave) {\n                // Usar função de salvamento fornecida pelo pai (suporta empresas específicas)\n                const success = await onSave(updatedPreferences);\n                if (success) {\n                    toast_success(\"Preferências de notificação salvas com sucesso!\");\n                }\n            } else {\n                toast_error(\"Erro: função de salvamento não fornecida\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao salvar preferências de notificação:\", error);\n            toast_error(\"Erro ao salvar preferências de notificação\");\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    if (searchMode && filteredNotifications.length === 0) {\n        return null;\n    }\n    const showNotifications = !searchMode || filteredNotifications.length > 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            showNotifications && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-6 bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-4\",\n                        children: \"Prefer\\xeancias de Notifica\\xe7\\xe3o\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                        lineNumber: 297,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-neutral-600 dark:text-neutral-300 mb-6\",\n                        children: [\n                            \"Configure quais tipos de notifica\\xe7\\xe3o estar\\xe3o dispon\\xedveis no sistema. Notifica\\xe7\\xf5es \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-blue-600 dark:text-blue-400\",\n                                children: \"habilitadas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 298,\n                                columnNumber: 160\n                            }, this),\n                            \" aparecer\\xe3o para os usu\\xe1rios. Notifica\\xe7\\xf5es \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-purple-600 dark:text-purple-400\",\n                                children: \"obrigat\\xf3rias\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 298,\n                                columnNumber: 286\n                            }, this),\n                            \" n\\xe3o podem ser desativadas pelos usu\\xe1rios.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                        lineNumber: 298,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: Object.entries(filteredCategories).map((param)=>{\n                            let [categoryKey, category] = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3 pb-2 border-b border-gray-200 dark:border-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"\".concat(getIconColors(category.moduleColor).text),\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                lineNumber: 305,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                        children: category.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                        children: category.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                lineNumber: 306,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                        lineNumber: 304,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-4\",\n                                        children: category.notifications.map((notification)=>{\n                                            const iconColors = getIconColors(category.moduleColor);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start gap-3 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"\".concat(iconColors.text),\n                                                                children: notification.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                                        children: notification.label\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                                        lineNumber: 327,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                                                                        children: notification.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ModuleCheckbox__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                id: \"enabled-\".concat(notification.key),\n                                                                checked: enabledNotifications[notification.key] || false,\n                                                                onChange: ()=>toggleNotificationEnabled(notification.key),\n                                                                disabled: isSaving,\n                                                                label: \"Habilitada no sistema\",\n                                                                moduleColor: \"admin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ModuleCheckbox__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                id: \"required-\".concat(notification.key),\n                                                                checked: requiredNotifications[notification.key] || false,\n                                                                onChange: ()=>toggleNotificationRequired(notification.key),\n                                                                disabled: !enabledNotifications[notification.key] || isSaving,\n                                                                label: \"Obrigat\\xf3ria para todos os usu\\xe1rios\",\n                                                                moduleColor: \"admin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, notification.key, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                lineNumber: 321,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                        lineNumber: 317,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, categoryKey, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 302,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                lineNumber: 296,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                className: \"text-blue-600 dark:text-blue-400\",\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 369,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                            lineNumber: 368,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-blue-900 dark:text-blue-100 mb-2\",\n                                    children: \"Como funcionam as notifica\\xe7\\xf5es\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-blue-800 dark:text-blue-200 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Habilitadas:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \" Aparecem nas configura\\xe7\\xf5es do usu\\xe1rio e podem ser ativadas/desativadas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                            lineNumber: 376,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Obrigat\\xf3rias:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \" Ficam sempre ativas e n\\xe3o podem ser desativadas pelo usu\\xe1rio\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                            lineNumber: 377,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Desabilitadas:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \" N\\xe3o aparecem no sistema e n\\xe3o s\\xe3o enviadas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                            lineNumber: 378,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• As configura\\xe7\\xf5es se aplicam a todos os usu\\xe1rios da empresa selecionada\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                            lineNumber: 379,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                    lineNumber: 375,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                            lineNumber: 371,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 367,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                lineNumber: 366,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\",\n                    onClick: handleSave,\n                    disabled: isSaving,\n                    children: isSaving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 394,\n                                columnNumber: 15\n                            }, this),\n                            \"Salvando...\"\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 399,\n                                columnNumber: 15\n                            }, this),\n                            \"Salvar Prefer\\xeancias\"\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 387,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                lineNumber: 386,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n        lineNumber: 293,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationPreferences, \"F7z/2EI6x6n56HARbqeEka7X2BE=\", false, function() {\n    return [\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = NotificationPreferences;\nvar _c;\n$RefreshReg$(_c, \"NotificationPreferences\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/preferences/NotificationPreferences.js\n"));

/***/ })

});