import { useState, useEffect } from "react";
import { Card } from "../../ui/Card";
import ModuleCheckbox from "../../ui/ModuleCheckbox";
import { Clock, Plus, X, Settings } from "lucide-react";

// Opções predefinidas de timing
const PREDEFINED_TIMINGS = [
  { value: 15, unit: 'minutes', label: '15 minutos antes' },
  { value: 30, unit: 'minutes', label: '30 minutos antes' },
  { value: 1, unit: 'hours', label: '1 hora antes' },
  { value: 2, unit: 'hours', label: '2 horas antes' },
  { value: 4, unit: 'hours', label: '4 horas antes' },
  { value: 1, unit: 'days', label: '1 dia antes' },
  { value: 2, unit: 'days', label: '2 dias antes' },
  { value: 7, unit: 'days', label: '1 semana antes' }
];

// Função para converter timing para minutos (para comparação e ordenação)
const timingToMinutes = (value, unit) => {
  switch (unit) {
    case 'minutes': return value;
    case 'hours': return value * 60;
    case 'days': return value * 60 * 24;
    default: return value;
  }
};

// Função para formatar timing para exibição
const formatTiming = (value, unit) => {
  if (unit === 'minutes') {
    return `${value} minuto${value !== 1 ? 's' : ''} antes`;
  } else if (unit === 'hours') {
    return `${value} hora${value !== 1 ? 's' : ''} antes`;
  } else if (unit === 'days') {
    if (value === 7) return '1 semana antes';
    return `${value} dia${value !== 1 ? 's' : ''} antes`;
  }
  return `${value} ${unit} antes`;
};

export default function AppointmentTimingSettings({ 
  timings = [], 
  onChange, 
  disabled = false 
}) {
  const [selectedTimings, setSelectedTimings] = useState([]);
  const [showCustomForm, setShowCustomForm] = useState(false);
  const [customValue, setCustomValue] = useState('');
  const [customUnit, setCustomUnit] = useState('minutes');

  // Inicializar com os timings recebidos
  useEffect(() => {
    if (timings && timings.length > 0) {
      setSelectedTimings(timings);
    } else {
      // Valor padrão: 1 hora antes
      setSelectedTimings([{ value: 1, unit: 'hours' }]);
    }
  }, [timings]);

  // Notificar mudanças para o componente pai
  useEffect(() => {
    if (onChange) {
      onChange(selectedTimings);
    }
  }, [selectedTimings, onChange]);

  // Adicionar timing predefinido
  const addPredefinedTiming = (timing) => {
    const exists = selectedTimings.some(t => 
      t.value === timing.value && t.unit === timing.unit
    );
    
    if (!exists) {
      const newTimings = [...selectedTimings, timing];
      // Ordenar por tempo (menor para maior)
      newTimings.sort((a, b) => timingToMinutes(a.value, a.unit) - timingToMinutes(b.value, b.unit));
      setSelectedTimings(newTimings);
    }
  };

  // Adicionar timing personalizado
  const addCustomTiming = () => {
    const value = parseInt(customValue);
    if (isNaN(value) || value <= 0) return;

    const timing = { value, unit: customUnit };
    const exists = selectedTimings.some(t => 
      t.value === timing.value && t.unit === timing.unit
    );
    
    if (!exists) {
      const newTimings = [...selectedTimings, timing];
      // Ordenar por tempo (menor para maior)
      newTimings.sort((a, b) => timingToMinutes(a.value, a.unit) - timingToMinutes(b.value, b.unit));
      setSelectedTimings(newTimings);
    }

    // Limpar formulário
    setCustomValue('');
    setCustomUnit('minutes');
    setShowCustomForm(false);
  };

  // Remover timing
  const removeTiming = (index) => {
    const newTimings = selectedTimings.filter((_, i) => i !== index);
    setSelectedTimings(newTimings);
  };

  // Verificar se um timing predefinido já está selecionado
  const isPredefinedSelected = (timing) => {
    return selectedTimings.some(t => 
      t.value === timing.value && t.unit === timing.unit
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 mb-3">
        <Settings className="text-purple-600 dark:text-purple-400" size={16} />
        <h6 className="font-medium text-gray-900 dark:text-white">
          Horários de Notificação
        </h6>
      </div>

      {/* Timings selecionados */}
      {selectedTimings.length > 0 && (
        <div className="space-y-2">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Horários configurados:
          </p>
          <div className="flex flex-wrap gap-2">
            {selectedTimings.map((timing, index) => (
              <div
                key={index}
                className="flex items-center gap-2 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm"
              >
                <Clock size={12} />
                <span>{formatTiming(timing.value, timing.unit)}</span>
                {!disabled && selectedTimings.length > 1 && (
                  <button
                    onClick={() => removeTiming(index)}
                    className="text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-200"
                  >
                    <X size={12} />
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {!disabled && (
        <>
          {/* Opções predefinidas */}
          <div className="space-y-2">
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Opções rápidas:
            </p>
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-2">
              {PREDEFINED_TIMINGS.map((timing, index) => (
                <button
                  key={index}
                  onClick={() => addPredefinedTiming(timing)}
                  disabled={isPredefinedSelected(timing)}
                  className={`p-2 text-xs rounded-lg border transition-colors ${
                    isPredefinedSelected(timing)
                      ? 'bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 border-gray-200 dark:border-gray-600 cursor-not-allowed'
                      : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-600 hover:bg-purple-50 dark:hover:bg-purple-900/20 hover:border-purple-300 dark:hover:border-purple-600'
                  }`}
                >
                  {timing.label}
                </button>
              ))}
            </div>
          </div>

          {/* Botão para adicionar timing personalizado */}
          {!showCustomForm && (
            <button
              onClick={() => setShowCustomForm(true)}
              className="flex items-center gap-2 text-sm text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-200"
            >
              <Plus size={14} />
              Adicionar horário personalizado
            </button>
          )}

          {/* Formulário de timing personalizado */}
          {showCustomForm && (
            <Card className="p-4 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600">
              <div className="space-y-3">
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Adicionar horário personalizado:
                </p>
                <div className="flex gap-2">
                  <input
                    type="number"
                    min="1"
                    value={customValue}
                    onChange={(e) => setCustomValue(e.target.value)}
                    placeholder="Valor"
                    className="flex-1 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                  <select
                    value={customUnit}
                    onChange={(e) => setCustomUnit(e.target.value)}
                    className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  >
                    <option value="minutes">Minutos</option>
                    <option value="hours">Horas</option>
                    <option value="days">Dias</option>
                  </select>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={addCustomTiming}
                    disabled={!customValue || parseInt(customValue) <= 0}
                    className="px-3 py-2 text-sm bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Adicionar
                  </button>
                  <button
                    onClick={() => {
                      setShowCustomForm(false);
                      setCustomValue('');
                      setCustomUnit('minutes');
                    }}
                    className="px-3 py-2 text-sm bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500"
                  >
                    Cancelar
                  </button>
                </div>
              </div>
            </Card>
          )}
        </>
      )}

      {/* Informação sobre como funciona */}
      <div className="text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
        <p className="font-medium mb-1">Como funciona:</p>
        <ul className="space-y-1">
          <li>• As notificações serão enviadas nos horários configurados antes da consulta</li>
          <li>• Você pode ter múltiplos horários (ex: 1 dia antes + 1 hora antes)</li>
          <li>• Os horários são ordenados automaticamente do menor para o maior</li>
        </ul>
      </div>
    </div>
  );
}
