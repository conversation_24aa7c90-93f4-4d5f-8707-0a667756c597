const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function updateAppointmentTimingPreferences() {
  try {
    console.log('🔄 Atualizando preferências de timing para notificações APPOINTMENT_COMING...');

    // Buscar todas as empresas
    const companies = await prisma.company.findMany({
      include: {
        companyPreferences: true
      }
    });

    console.log(`📊 Encontradas ${companies.length} empresas para atualizar`);

    for (const company of companies) {
      console.log(`\n🏢 Processando empresa: ${company.name}`);

      let companyPreference = company.companyPreferences;

      if (!companyPreference) {
        // Criar preferência padrão se não existir
        companyPreference = await prisma.companyPreference.create({
          data: {
            companyId: company.id,
            theme: 'light',
            language: 'pt-BR',
            timezone: 'America/Sao_Paulo',
            dateFormat: 'DD/MM/YYYY',
            timeFormat: '24h',
            currency: 'BRL',
            notifications: {
              enabled: {
                NEW_REGISTRATION: false,
                APPOINTMENT_COMING: false,
                NEW_ACCESS: false,
                NEW_BACKUP: false,
                NEW_EXPORT: false,
                NEW_APPOINTMENT_SCHEDULED: false,
                APPOINTMENT_SCHEDULED_FOR_PROVIDER: false,
                APPOINTMENT_SCHEDULED_FOR_PATIENT: false,
                DOCUMENT_SHARED: false,
                NEW_APPOINTMENT_REQUEST: false,
                APPOINTMENT_REQUEST_APPROVED: false,
                APPOINTMENT_REQUEST_SUGGESTED: false,
                APPOINTMENT_REQUEST_REJECTED: false
              },
              required: {
                NEW_REGISTRATION: false,
                APPOINTMENT_COMING: false,
                NEW_ACCESS: false,
                NEW_BACKUP: false,
                NEW_EXPORT: false,
                NEW_APPOINTMENT_SCHEDULED: false,
                APPOINTMENT_SCHEDULED_FOR_PROVIDER: false,
                APPOINTMENT_SCHEDULED_FOR_PATIENT: false,
                DOCUMENT_SHARED: false,
                NEW_APPOINTMENT_REQUEST: false,
                APPOINTMENT_REQUEST_APPROVED: false,
                APPOINTMENT_REQUEST_SUGGESTED: false,
                APPOINTMENT_REQUEST_REJECTED: false
              },
              timings: {
                APPOINTMENT_COMING: [{ value: 1, unit: 'hours' }]
              }
            }
          }
        });
        console.log(`  ✓ Preferência criada para ${company.name} com timing padrão`);
      } else {
        // Atualizar preferência existente para incluir timings
        const currentNotifications = companyPreference.notifications || { enabled: {}, required: {} };
        
        // Adicionar timings se não existir
        if (!currentNotifications.timings) {
          currentNotifications.timings = {
            APPOINTMENT_COMING: [{ value: 1, unit: 'hours' }]
          };

          await prisma.companyPreference.update({
            where: { id: companyPreference.id },
            data: {
              notifications: currentNotifications
            }
          });
          console.log(`  ✓ Timing padrão adicionado para ${company.name}`);
        } else if (!currentNotifications.timings.APPOINTMENT_COMING) {
          // Adicionar timing para APPOINTMENT_COMING se não existir
          currentNotifications.timings.APPOINTMENT_COMING = [{ value: 1, unit: 'hours' }];

          await prisma.companyPreference.update({
            where: { id: companyPreference.id },
            data: {
              notifications: currentNotifications
            }
          });
          console.log(`  ✓ Timing APPOINTMENT_COMING adicionado para ${company.name}`);
        } else {
          console.log(`  ℹ️  ${company.name} já possui configurações de timing`);
        }
      }
    }

    console.log('\n✅ Atualização de preferências de timing concluída com sucesso!');
    console.log('\n📋 Resumo das configurações padrão aplicadas:');
    console.log('   • APPOINTMENT_COMING: 1 hora antes (padrão)');
    console.log('   • Os usuários podem personalizar os horários através das configurações');

  } catch (error) {
    console.error('❌ Erro ao atualizar preferências de timing:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Executar o script se chamado diretamente
if (require.main === module) {
  updateAppointmentTimingPreferences()
    .then(() => {
      console.log('\n🎉 Script executado com sucesso!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Erro na execução do script:', error);
      process.exit(1);
    });
}

module.exports = updateAppointmentTimingPreferences;
