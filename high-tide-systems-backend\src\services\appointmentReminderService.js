const cron = require('node-cron');
const prisma = require('../utils/prisma');
const { getIO, sendNewNotification, updateUnreadCount } = require('../utils/socket');

class AppointmentReminderService {
  constructor() {
    // Executa a cada 15 minutos para verificar consultas próximas
    this.job = cron.schedule('*/15 * * * *', this.checkUpcomingAppointments.bind(this), {
      scheduled: false
    });
  }

  /**
   * Inicia o serviço de lembretes
   */
  start() {
    this.job.start();
    console.log('[AppointmentReminderService] Serviço de lembretes iniciado - Executa a cada 15 minutos');
  }

  /**
   * Para o serviço de lembretes
   */
  stop() {
    this.job.stop();
    console.log('[AppointmentReminderService] Serviço de lembretes parado');
  }

  /**
   * Converte timing para minutos
   */
  timingToMinutes(value, unit) {
    switch (unit) {
      case 'minutes': return value;
      case 'hours': return value * 60;
      case 'days': return value * 60 * 24;
      default: return value;
    }
  }

  /**
   * Formata timing para exibição
   */
  formatTiming(value, unit) {
    if (unit === 'minutes') {
      return `${value} minuto${value !== 1 ? 's' : ''} antes`;
    } else if (unit === 'hours') {
      return `${value} hora${value !== 1 ? 's' : ''} antes`;
    } else if (unit === 'days') {
      if (value === 7) return '1 semana antes';
      return `${value} dia${value !== 1 ? 's' : ''} antes`;
    }
    return `${value} ${unit} antes`;
  }

  /**
   * Verifica consultas próximas e envia notificações
   */
  async checkUpcomingAppointments() {
    try {
      console.log('[AppointmentReminderService] Verificando consultas próximas...');
      
      const now = new Date();
      
      // Buscar todas as empresas com suas preferências de notificação
      const companies = await prisma.company.findMany({
        include: {
          companyPreferences: true
        }
      });

      for (const company of companies) {
        await this.processCompanyAppointments(company, now);
      }

    } catch (error) {
      console.error('[AppointmentReminderService] Erro ao verificar consultas próximas:', error);
    }
  }

  /**
   * Processa consultas de uma empresa específica
   */
  async processCompanyAppointments(company, now) {
    try {
      // Obter configurações de notificação da empresa
      const preferences = company.companyPreferences;
      if (!preferences || !preferences.notifications) {
        return; // Empresa sem configurações de notificação
      }

      const notifications = preferences.notifications;
      
      // Verificar se APPOINTMENT_COMING está habilitada
      if (!notifications.enabled?.APPOINTMENT_COMING) {
        return; // Notificação não habilitada para esta empresa
      }

      // Obter timings configurados (padrão: 1 hora antes)
      const timings = notifications.timings?.APPOINTMENT_COMING || [{ value: 1, unit: 'hours' }];

      // Para cada timing, verificar consultas que precisam de notificação
      for (const timing of timings) {
        await this.processTimingNotifications(company.id, timing, now);
      }

    } catch (error) {
      console.error(`[AppointmentReminderService] Erro ao processar empresa ${company.name}:`, error);
    }
  }

  /**
   * Processa notificações para um timing específico
   */
  async processTimingNotifications(companyId, timing, now) {
    try {
      const minutesBefore = this.timingToMinutes(timing.value, timing.unit);
      
      // Calcular o momento exato quando a notificação deve ser enviada
      const notificationTime = new Date(now.getTime() + minutesBefore * 60 * 1000);
      
      // Margem de tolerância de 15 minutos (para não perder notificações)
      const startRange = new Date(notificationTime.getTime() - 7.5 * 60 * 1000);
      const endRange = new Date(notificationTime.getTime() + 7.5 * 60 * 1000);

      // Buscar consultas que começam no período alvo
      const appointments = await prisma.scheduling.findMany({
        where: {
          companyId: companyId,
          startDate: {
            gte: startRange,
            lte: endRange
          },
          status: {
            in: ['PENDING', 'CONFIRMED']
          }
        },
        include: {
          Person: {
            include: {
              clientPersons: {
                include: {
                  client: true
                }
              }
            }
          },
          provider: true,
          serviceType: true,
          location: true
        }
      });

      console.log(`[AppointmentReminderService] Encontradas ${appointments.length} consultas para notificar (${this.formatTiming(timing.value, timing.unit)})`);

      // Processar cada consulta
      for (const appointment of appointments) {
        await this.sendAppointmentReminder(appointment, timing);
      }

    } catch (error) {
      console.error(`[AppointmentReminderService] Erro ao processar timing ${timing.value} ${timing.unit}:`, error);
    }
  }

  /**
   * Envia lembrete para uma consulta específica
   */
  async sendAppointmentReminder(appointment, timing) {
    try {
      // Verificar se já foi enviada uma notificação para este timing
      const existingNotification = await prisma.notification.findFirst({
        where: {
          type: 'APPOINTMENT_COMING',
          AND: [
            {
              data: {
                path: ['schedulingId'],
                equals: appointment.id
              }
            },
            {
              data: {
                path: ['timingValue'],
                equals: timing.value
              }
            },
            {
              data: {
                path: ['timingUnit'],
                equals: timing.unit
              }
            }
          ],
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Últimas 24 horas
          }
        }
      });

      if (existingNotification) {
        return; // Já foi enviada notificação para este timing
      }

      const timingText = this.formatTiming(timing.value, timing.unit);
      const appointmentDate = appointment.startDate.toLocaleDateString('pt-BR');
      const appointmentTime = appointment.startDate.toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit'
      });

      // Buscar usuários que devem receber a notificação
      const usersToNotify = await this.getUsersToNotify(appointment);

      const notifications = [];
      
      for (const user of usersToNotify) {
        const title = 'Consulta Próxima';
        const message = `Sua consulta "${appointment.title}" está marcada para ${appointmentDate} às ${appointmentTime} (${timingText})`;

        notifications.push({
          title,
          message,
          type: 'APPOINTMENT_COMING',
          priority: 'HIGH',
          userId: user.id,
          userType: 'USER',
          companyId: appointment.companyId,
          data: {
            schedulingId: appointment.id,
            schedulingTitle: appointment.title,
            timingValue: timing.value,
            timingUnit: timing.unit,
            appointmentDate: appointmentDate,
            appointmentTime: appointmentTime,
            createdAt: new Date()
          }
        });
      }

      if (notifications.length > 0) {
        await prisma.notification.createMany({ data: notifications });

        // Enviar via WebSocket
        const io = getIO();
        if (io) {
          for (const notification of notifications) {
            const createdNotification = await prisma.notification.findFirst({
              where: {
                userId: notification.userId,
                type: 'APPOINTMENT_COMING',
                createdAt: { gte: new Date(Date.now() - 5000) }
              },
              orderBy: { createdAt: 'desc' }
            });

            if (createdNotification) {
              sendNewNotification(io, notification.userId, createdNotification);
              
              const unreadCount = await prisma.notification.count({
                where: { userId: notification.userId, read: false }
              });
              updateUnreadCount(io, notification.userId, unreadCount);
            }
          }
        }

        console.log(`[AppointmentReminderService] ${notifications.length} notificações de lembrete enviadas para consulta ${appointment.id}`);
      }

    } catch (error) {
      console.error(`[AppointmentReminderService] Erro ao enviar lembrete para consulta ${appointment.id}:`, error);
    }
  }

  /**
   * Obtém usuários que devem receber notificação para uma consulta
   */
  async getUsersToNotify(appointment) {
    const users = [];

    // Adicionar o profissional responsável
    if (appointment.provider) {
      users.push(appointment.provider);
    }

    // Adicionar usuários com permissão para receber notificações de consultas próximas
    const usersWithPermission = await prisma.user.findMany({
      where: {
        companyId: appointment.companyId,
        isActive: true,
        userPermissions: {
          some: {
            permission: {
              name: 'notifications.appointment_coming.receive'
            }
          }
        }
      }
    });

    // Adicionar usuários únicos
    for (const user of usersWithPermission) {
      if (!users.find(u => u.id === user.id)) {
        users.push(user);
      }
    }

    return users;
  }
}

module.exports = new AppointmentReminderService();
