import React, { useState, useEffect } from 'react';
import { Card } from '../../ui/Card';
import Button from '../../ui/Button';
import ModuleCheckbox from '../../ui/ModuleCheckbox';
import { useToast } from '@/contexts/ToastContext';
import { api } from '@/utils/api';
import { Bell, UserPlus, Clock, Shield, Database, Download, AlertCircle, Calendar, UserCheck, Users } from 'lucide-react';

// Definir tipos de notificação
const NOTIFICATION_TYPES = [
  { 
    key: "NEW_REGISTRATION", 
    label: "Novos Cadastros",
    description: "Notificações sobre novos cadastros no sistema",
    icon: <UserPlus size={16} />
  },
  {
    key: "APPOINTMENT_COMING",
    label: "Consultas Chegando",
    description: "Avisos de consultas agendadas com horários configuráveis",
    icon: <Clock size={16} />
  },
  { 
    key: "NEW_ACCESS", 
    label: "Novos Acessos",
    description: "Notificações sobre novos acessos ao sistema",
    icon: <Shield size={16} />
  },
  { 
    key: "NEW_BACKUP", 
    label: "Novo Backup",
    description: "Confirmação de backups realizados",
    icon: <Database size={16} />
  },
  { 
    key: "NEW_EXPORT", 
    label: "Exportações",
    description: "Notificações sobre exportações concluídas",
    icon: <Download size={16} />
  },
  { 
    key: "SYSTEM_ALERT", 
    label: "Alertas do Sistema",
    description: "Alertas importantes do sistema",
    icon: <AlertCircle size={16} />
  },
  { 
    key: "NEW_APPOINTMENT_SCHEDULED", 
    label: "Nova Consulta Agendada",
    description: "Notificações quando uma nova consulta é agendada no sistema",
    icon: <Calendar size={16} />
  },
  { 
    key: "APPOINTMENT_SCHEDULED_FOR_PROVIDER", 
    label: "Consulta Agendada (Profissional)",
    description: "Notificar profissionais quando consultas são agendadas para eles",
    icon: <UserCheck size={16} />
  },

];

const NotificationPermissionsTab = ({ userId, onClose }) => {
  const [permissions, setPermissions] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const { toast_success, toast_error } = useToast();

  // Carregar permissões do usuário
  useEffect(() => {
    if (userId) {
      loadUserPermissions();
    }
  }, [userId]);

  const loadUserPermissions = async () => {
    try {
      setIsLoading(true);
      const response = await api.get(`/notification-permissions/user/${userId}`);
      const userPermissions = response.data.data || {};
      
      // Converter para formato do checkbox
      const permissionState = {};
      NOTIFICATION_TYPES.forEach(type => {
        permissionState[type.key] = userPermissions[type.key]?.canReceive || false;
      });
      
      setPermissions(permissionState);
    } catch (error) {
      console.error('Erro ao carregar permissões:', error);
      toast_error('Erro ao carregar permissões de notificação');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePermissionChange = (key, value) => {
    setPermissions(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      
      await api.put(`/notification-permissions/user/${userId}`, permissions);
      
      toast_success('Permissões de notificação atualizadas com sucesso!');
      onClose();
    } catch (error) {
      console.error('Erro ao salvar permissões:', error);
      toast_error('Erro ao salvar permissões de notificação');
    } finally {
      setIsSaving(false);
    }
  };

  const handleRemoveAll = async () => {
    if (!window.confirm('Tem certeza que deseja remover todas as permissões de notificação deste usuário?')) {
      return;
    }

    try {
      setIsSaving(true);
      
      await api.delete(`/notification-permissions/user/${userId}`);
      
      // Resetar estado local
      const resetPermissions = {};
      NOTIFICATION_TYPES.forEach(type => {
        resetPermissions[type.key] = false;
      });
      setPermissions(resetPermissions);
      
      toast_success('Todas as permissões de notificação foram removidas');
    } catch (error) {
      console.error('Erro ao remover permissões:', error);
      toast_error('Erro ao remover permissões de notificação');
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"></div>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Bell className="text-cyan-600" size={20} />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Permissões de Notificação
          </h3>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRemoveAll}
            disabled={isSaving}
          >
            Remover Todas
          </Button>
          <Button
            onClick={onClose}
            variant="outline"
            size="sm"
          >
            Fechar
          </Button>
        </div>
      </div>


      <div className="space-y-4 mb-6">
        {NOTIFICATION_TYPES.map((notification) => (
          <div key={notification.key} className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-3">
                <div className="p-2 bg-cyan-100 dark:bg-cyan-900 rounded-lg text-cyan-600 dark:text-cyan-400">
                  {notification.icon}
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    {notification.label}
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {notification.description}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center">
                <ModuleCheckbox
                  id={`permission-${notification.key}`}
                  checked={permissions[notification.key] || false}
                  onChange={(e) => handlePermissionChange(notification.key, e.target.checked)}
                  disabled={isSaving}
                />
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 mb-6">
        <div className="flex items-start gap-3">
          <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
            <Bell className="text-blue-600 dark:text-blue-400" size={16} />
          </div>
          <div>
            <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
              Sobre as Permissões de Notificação
            </h4>
            <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
              <li>• Usuários só podem receber notificações que têm permissão</li>
              <li>• Mesmo com permissão, o usuário pode desabilitar individualmente</li>
              <li>• Notificações obrigatórias da empresa sobrepõem as permissões individuais</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="flex justify-end">
        <Button 
          onClick={handleSave}
          disabled={isSaving}
          className="px-6 py-2"
        >
          {isSaving ? "Salvando..." : "Salvar Permissões"}
        </Button>
      </div>
    </Card>
  );
};

export default NotificationPermissionsTab;
