{"timestamp":"2025-08-25 19:20:30","level":"error","message":"uncaughtException: Cannot find module '../utils/socket'\nRequire stack:\n- /usr/src/app/src/services/appointmentReminderService.js\n- /usr/src/app/src/server.js\nError: Cannot find module '../utils/socket'\nRequire stack:\n- /usr/src/app/src/services/appointmentReminderService.js\n- /usr/src/app/src/server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Module._load (node:internal/modules/cjs/loader:1051:27)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/services/appointmentReminderService.js:3:59)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)","error":{"code":"MODULE_NOT_FOUND","requireStack":["/usr/src/app/src/services/appointmentReminderService.js","/usr/src/app/src/server.js"]},"stack":"Error: Cannot find module '../utils/socket'\nRequire stack:\n- /usr/src/app/src/services/appointmentReminderService.js\n- /usr/src/app/src/server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Module._load (node:internal/modules/cjs/loader:1051:27)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/services/appointmentReminderService.js:3:59)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)","exception":true,"date":"Mon Aug 25 2025 19:20:30 GMT+0000 (Coordinated Universal Time)","process":{"pid":390,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":161820672,"heapTotal":106663936,"heapUsed":80041160,"external":3226008,"arrayBuffers":98464}},"os":{"loadavg":[2.76,2.36,2.18],"uptime":11980.14},"trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1225,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1051,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":59,"file":"/usr/src/app/src/services/appointmentReminderService.js","function":null,"line":3,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1469,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1548,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1288,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1104,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}
{"timestamp":"2025-08-25 19:20:42","level":"error","message":"uncaughtException: Cannot find module '../utils/socket'\nRequire stack:\n- /usr/src/app/src/services/appointmentReminderService.js\n- /usr/src/app/src/server.js\nError: Cannot find module '../utils/socket'\nRequire stack:\n- /usr/src/app/src/services/appointmentReminderService.js\n- /usr/src/app/src/server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Module._load (node:internal/modules/cjs/loader:1051:27)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/services/appointmentReminderService.js:3:59)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)","error":{"code":"MODULE_NOT_FOUND","requireStack":["/usr/src/app/src/services/appointmentReminderService.js","/usr/src/app/src/server.js"]},"stack":"Error: Cannot find module '../utils/socket'\nRequire stack:\n- /usr/src/app/src/services/appointmentReminderService.js\n- /usr/src/app/src/server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Module._load (node:internal/modules/cjs/loader:1051:27)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/services/appointmentReminderService.js:3:59)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)","exception":true,"date":"Mon Aug 25 2025 19:20:42 GMT+0000 (Coordinated Universal Time)","process":{"pid":457,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":161603584,"heapTotal":106139648,"heapUsed":80368208,"external":3226008,"arrayBuffers":98464}},"os":{"loadavg":[2.74,2.36,2.19],"uptime":11992.38},"trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1225,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1051,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":59,"file":"/usr/src/app/src/services/appointmentReminderService.js","function":null,"line":3,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1469,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1548,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1288,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1104,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}
{"timestamp":"2025-08-25 19:20:53","level":"error","message":"uncaughtException: Cannot find module '../utils/socket'\nRequire stack:\n- /usr/src/app/src/services/appointmentReminderService.js\n- /usr/src/app/src/server.js\nError: Cannot find module '../utils/socket'\nRequire stack:\n- /usr/src/app/src/services/appointmentReminderService.js\n- /usr/src/app/src/server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Module._load (node:internal/modules/cjs/loader:1051:27)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/services/appointmentReminderService.js:3:59)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)","error":{"code":"MODULE_NOT_FOUND","requireStack":["/usr/src/app/src/services/appointmentReminderService.js","/usr/src/app/src/server.js"]},"stack":"Error: Cannot find module '../utils/socket'\nRequire stack:\n- /usr/src/app/src/services/appointmentReminderService.js\n- /usr/src/app/src/server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Module._load (node:internal/modules/cjs/loader:1051:27)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/services/appointmentReminderService.js:3:59)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)","exception":true,"date":"Mon Aug 25 2025 19:20:53 GMT+0000 (Coordinated Universal Time)","process":{"pid":524,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":161009664,"heapTotal":105615360,"heapUsed":80749224,"external":3226008,"arrayBuffers":98464}},"os":{"loadavg":[2.32,2.28,2.16],"uptime":12003.28},"trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1225,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1051,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":59,"file":"/usr/src/app/src/services/appointmentReminderService.js","function":null,"line":3,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1469,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1548,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1288,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1104,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}
{"timestamp":"2025-08-25 19:21:06","level":"error","message":"uncaughtException: Cannot find module '../utils/socket'\nRequire stack:\n- /usr/src/app/src/services/appointmentReminderService.js\n- /usr/src/app/src/server.js\nError: Cannot find module '../utils/socket'\nRequire stack:\n- /usr/src/app/src/services/appointmentReminderService.js\n- /usr/src/app/src/server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Module._load (node:internal/modules/cjs/loader:1051:27)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/services/appointmentReminderService.js:3:59)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)","error":{"code":"MODULE_NOT_FOUND","requireStack":["/usr/src/app/src/services/appointmentReminderService.js","/usr/src/app/src/server.js"]},"stack":"Error: Cannot find module '../utils/socket'\nRequire stack:\n- /usr/src/app/src/services/appointmentReminderService.js\n- /usr/src/app/src/server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Module._load (node:internal/modules/cjs/loader:1051:27)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/services/appointmentReminderService.js:3:59)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)","exception":true,"date":"Mon Aug 25 2025 19:21:06 GMT+0000 (Coordinated Universal Time)","process":{"pid":591,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":162516992,"heapTotal":106926080,"heapUsed":79806560,"external":3226008,"arrayBuffers":98464}},"os":{"loadavg":[2.2,2.25,2.15],"uptime":12016.07},"trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1225,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1051,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":59,"file":"/usr/src/app/src/services/appointmentReminderService.js","function":null,"line":3,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1469,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1548,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1288,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1104,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}
{"timestamp":"2025-08-25 19:27:18","level":"error","message":"uncaughtException: Cannot find module '../utils/socket'\nRequire stack:\n- /usr/src/app/src/services/appointmentReminderService.js\n- /usr/src/app/src/server.js\nError: Cannot find module '../utils/socket'\nRequire stack:\n- /usr/src/app/src/services/appointmentReminderService.js\n- /usr/src/app/src/server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Module._load (node:internal/modules/cjs/loader:1051:27)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/services/appointmentReminderService.js:3:59)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)","error":{"code":"MODULE_NOT_FOUND","requireStack":["/usr/src/app/src/services/appointmentReminderService.js","/usr/src/app/src/server.js"]},"stack":"Error: Cannot find module '../utils/socket'\nRequire stack:\n- /usr/src/app/src/services/appointmentReminderService.js\n- /usr/src/app/src/server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Module._load (node:internal/modules/cjs/loader:1051:27)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/services/appointmentReminderService.js:3:59)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)","exception":true,"date":"Mon Aug 25 2025 19:27:18 GMT+0000 (Coordinated Universal Time)","process":{"pid":138,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":160903168,"heapTotal":105877504,"heapUsed":80321680,"external":3226008,"arrayBuffers":98464}},"os":{"loadavg":[2.14,2.16,2.16],"uptime":12388.15},"trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1225,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1051,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":59,"file":"/usr/src/app/src/services/appointmentReminderService.js","function":null,"line":3,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1469,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1548,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1288,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1104,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}
