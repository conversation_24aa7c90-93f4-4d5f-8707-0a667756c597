"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/admin/preferences/NotificationPreferences.js":
/*!*********************************************************************!*\
  !*** ./src/components/admin/preferences/NotificationPreferences.js ***!
  \*********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationPreferences)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.js\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.js\");\n/* harmony import */ var _ui_ModuleCheckbox__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../ui/ModuleCheckbox */ \"(app-pages-browser)/./src/components/ui/ModuleCheckbox.js\");\n/* harmony import */ var _PreferencesSection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./PreferencesSection */ \"(app-pages-browser)/./src/components/admin/preferences/PreferencesSection.js\");\n/* harmony import */ var _services_preferencesService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/services/preferencesService */ \"(app-pages-browser)/./src/services/preferencesService.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-check.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _AppointmentTimingSettings__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./AppointmentTimingSettings */ \"(app-pages-browser)/./src/components/admin/preferences/AppointmentTimingSettings.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Definir tipos de notificação organizados por categoria\nconst NOTIFICATION_CATEGORIES = {\n    appointments: {\n        title: \"Consultas\",\n        description: \"Notificações relacionadas a agendamentos e consultas\",\n        moduleColor: \"scheduler\",\n        notifications: [\n            {\n                key: \"APPOINTMENT_COMING\",\n                label: \"Consultas Chegando\",\n                description: \"Avisos de consultas agendadas (1 hora antes)\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 22,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"NEW_APPOINTMENT_SCHEDULED\",\n                label: \"Nova Consulta Agendada\",\n                description: \"Notificações quando uma nova consulta é agendada no sistema\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 28,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"APPOINTMENT_SCHEDULED_FOR_PROVIDER\",\n                label: \"Consulta Agendada (Profissional)\",\n                description: \"Notificar profissionais quando consultas são agendadas para eles\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 34,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"APPOINTMENT_SCHEDULED_FOR_PATIENT\",\n                label: \"Consulta Agendada (Paciente)\",\n                description: \"Notificar pacientes/clientes quando consultas são agendadas\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 40,\n                    columnNumber: 15\n                }, undefined)\n            }\n        ]\n    },\n    requests: {\n        title: \"Solicitações\",\n        description: \"Notificações sobre solicitações de agendamento\",\n        moduleColor: \"scheduler\",\n        notifications: [\n            {\n                key: \"NEW_APPOINTMENT_REQUEST\",\n                label: \"Nova Solicitação de Agendamento\",\n                description: \"Notificações quando clientes criam novas solicitações de agendamento\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 53,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"APPOINTMENT_REQUEST_APPROVED\",\n                label: \"Solicitação Aprovada\",\n                description: \"Notificar clientes quando suas solicitações são aprovadas\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 59,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"APPOINTMENT_REQUEST_SUGGESTED\",\n                label: \"Sugestão de Alteração\",\n                description: \"Notificar clientes quando há sugestões de alteração em suas solicitações\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 65,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"APPOINTMENT_REQUEST_REJECTED\",\n                label: \"Solicitação Rejeitada\",\n                description: \"Notificar clientes quando suas solicitações são rejeitadas\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 71,\n                    columnNumber: 15\n                }, undefined)\n            }\n        ]\n    },\n    registrations: {\n        title: \"Novos Registros\",\n        description: \"Notificações sobre novos cadastros e acessos\",\n        moduleColor: \"admin\",\n        notifications: [\n            {\n                key: \"NEW_REGISTRATION\",\n                label: \"Novos Cadastros\",\n                description: \"Notificações sobre novos cadastros no sistema\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 84,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"NEW_ACCESS\",\n                label: \"Novos Acessos\",\n                description: \"Notificações sobre novos acessos ao sistema\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 90,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"NEW_BACKUP\",\n                label: \"Novo Backup\",\n                description: \"Confirmação de backups realizados\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 96,\n                    columnNumber: 15\n                }, undefined)\n            }\n        ]\n    },\n    documents: {\n        title: \"Documentos/Exportações\",\n        description: \"Notificações sobre documentos e exportações\",\n        moduleColor: \"people\",\n        notifications: [\n            {\n                key: \"DOCUMENT_SHARED\",\n                label: \"Documentos Compartilhados\",\n                description: \"Notificações quando documentos são compartilhados com usuários ou clientes\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 109,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"NEW_EXPORT\",\n                label: \"Exportações\",\n                description: \"Notificações sobre exportações concluídas\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 115,\n                    columnNumber: 15\n                }, undefined)\n            }\n        ]\n    }\n};\n// Função para obter todas as notificações em formato plano (para compatibilidade)\nconst getAllNotifications = ()=>{\n    const allNotifications = [];\n    Object.values(NOTIFICATION_CATEGORIES).forEach((category)=>{\n        allNotifications.push(...category.notifications);\n    });\n    return allNotifications;\n};\nconst NOTIFICATION_TYPES = getAllNotifications();\n// Função para obter as cores do ícone baseado no módulo\nconst getIconColors = (moduleColor)=>{\n    const colorMap = {\n        scheduler: {\n            text: \"text-purple-600 dark:text-purple-400\"\n        },\n        people: {\n            text: \"text-orange-600 dark:text-orange-400\"\n        },\n        admin: {\n            text: \"text-gray-600 dark:text-gray-400\"\n        },\n        default: {\n            text: \"text-cyan-600 dark:text-cyan-400\"\n        }\n    };\n    return colorMap[moduleColor] || colorMap.default;\n};\nfunction NotificationPreferences(param) {\n    let { search = \"\", searchMode = false, preferences = null, selectedCompanyId = null, onSave = null } = param;\n    _s();\n    // Estados para preferências de notificação\n    const [enabledNotifications, setEnabledNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [requiredNotifications, setRequiredNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [appointmentTimings, setAppointmentTimings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    // Carregar preferências quando o componente receber os dados\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationPreferences.useEffect\": ()=>{\n            if (preferences && preferences.notifications) {\n                setEnabledNotifications(preferences.notifications.enabled || {});\n                setRequiredNotifications(preferences.notifications.required || {});\n            } else {\n                // Definir valores padrão se não houver preferências\n                const defaultEnabled = {};\n                const defaultRequired = {};\n                NOTIFICATION_TYPES.forEach({\n                    \"NotificationPreferences.useEffect\": (type)=>{\n                        defaultEnabled[type.key] = false;\n                        defaultRequired[type.key] = false;\n                    }\n                }[\"NotificationPreferences.useEffect\"]);\n                setEnabledNotifications(defaultEnabled);\n                setRequiredNotifications(defaultRequired);\n            }\n        }\n    }[\"NotificationPreferences.useEffect\"], [\n        preferences\n    ]);\n    // Filtrar categorias baseado na pesquisa\n    const filteredCategories = searchMode && search ? Object.entries(NOTIFICATION_CATEGORIES).reduce((acc, param)=>{\n        let [key, category] = param;\n        const filteredNotifications = category.notifications.filter((notification)=>notification.label.toLowerCase().includes(search.toLowerCase()) || notification.description.toLowerCase().includes(search.toLowerCase()));\n        if (filteredNotifications.length > 0) {\n            acc[key] = {\n                ...category,\n                notifications: filteredNotifications\n            };\n        }\n        return acc;\n    }, {}) : NOTIFICATION_CATEGORIES;\n    // Manter compatibilidade com filteredNotifications para o resto do código\n    const filteredNotifications = searchMode && search ? NOTIFICATION_TYPES.filter((notification)=>notification.label.toLowerCase().includes(search.toLowerCase()) || notification.description.toLowerCase().includes(search.toLowerCase())) : NOTIFICATION_TYPES;\n    // Se não há resultados na pesquisa, não mostrar nada\n    if (searchMode && Object.keys(filteredCategories).length === 0) {\n        return null;\n    }\n    // Função para toggle de notificação habilitada\n    const toggleNotificationEnabled = (key)=>{\n        setEnabledNotifications((prev)=>({\n                ...prev,\n                [key]: !prev[key]\n            }));\n        // Se desabilitar, também desabilitar obrigatoriedade\n        if (enabledNotifications[key]) {\n            setRequiredNotifications((prev)=>({\n                    ...prev,\n                    [key]: false\n                }));\n        }\n    };\n    // Função para toggle de obrigatoriedade\n    const toggleNotificationRequired = (key)=>{\n        setRequiredNotifications((prev)=>({\n                ...prev,\n                [key]: !prev[key]\n            }));\n    };\n    // Função para salvar preferências\n    const handleSave = async ()=>{\n        setIsSaving(true);\n        try {\n            const notificationPreferences = {\n                enabled: enabledNotifications,\n                required: requiredNotifications\n            };\n            // Manter as preferências existentes e adicionar as de notificação\n            const currentPreferences = preferences || {};\n            const updatedPreferences = {\n                ...currentPreferences,\n                notifications: notificationPreferences\n            };\n            if (onSave) {\n                // Usar função de salvamento fornecida pelo pai (suporta empresas específicas)\n                const success = await onSave(updatedPreferences);\n                if (success) {\n                    toast_success(\"Preferências de notificação salvas com sucesso!\");\n                }\n            } else {\n                toast_error(\"Erro: função de salvamento não fornecida\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao salvar preferências de notificação:\", error);\n            toast_error(\"Erro ao salvar preferências de notificação\");\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    if (searchMode && filteredNotifications.length === 0) {\n        return null;\n    }\n    const showNotifications = !searchMode || filteredNotifications.length > 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            showNotifications && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-6 bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-4\",\n                        children: \"Prefer\\xeancias de Notifica\\xe7\\xe3o\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                        lineNumber: 281,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-neutral-600 dark:text-neutral-300 mb-6\",\n                        children: [\n                            \"Configure quais tipos de notifica\\xe7\\xe3o estar\\xe3o dispon\\xedveis no sistema. Notifica\\xe7\\xf5es \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-blue-600 dark:text-blue-400\",\n                                children: \"habilitadas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 282,\n                                columnNumber: 160\n                            }, this),\n                            \" aparecer\\xe3o para os usu\\xe1rios. Notifica\\xe7\\xf5es \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-purple-600 dark:text-purple-400\",\n                                children: \"obrigat\\xf3rias\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 282,\n                                columnNumber: 286\n                            }, this),\n                            \" n\\xe3o podem ser desativadas pelos usu\\xe1rios.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                        lineNumber: 282,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: Object.entries(filteredCategories).map((param)=>{\n                            let [categoryKey, category] = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3 pb-2 border-b border-gray-200 dark:border-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"\".concat(getIconColors(category.moduleColor).text),\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                        children: category.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                        children: category.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-4\",\n                                        children: category.notifications.map((notification)=>{\n                                            const iconColors = getIconColors(category.moduleColor);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start gap-3 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"\".concat(iconColors.text),\n                                                                children: notification.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                                        children: notification.label\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                                        lineNumber: 311,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                                                                        children: notification.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                                        lineNumber: 314,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ModuleCheckbox__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                id: \"enabled-\".concat(notification.key),\n                                                                checked: enabledNotifications[notification.key] || false,\n                                                                onChange: ()=>toggleNotificationEnabled(notification.key),\n                                                                disabled: isSaving,\n                                                                label: \"Habilitada no sistema\",\n                                                                moduleColor: \"admin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ModuleCheckbox__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                id: \"required-\".concat(notification.key),\n                                                                checked: requiredNotifications[notification.key] || false,\n                                                                onChange: ()=>toggleNotificationRequired(notification.key),\n                                                                disabled: !enabledNotifications[notification.key] || isSaving,\n                                                                label: \"Obrigat\\xf3ria para todos os usu\\xe1rios\",\n                                                                moduleColor: \"admin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, notification.key, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                lineNumber: 305,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                        lineNumber: 301,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, categoryKey, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 286,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                lineNumber: 280,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                className: \"text-blue-600 dark:text-blue-400\",\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                            lineNumber: 352,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-blue-900 dark:text-blue-100 mb-2\",\n                                    children: \"Como funcionam as notifica\\xe7\\xf5es\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                    lineNumber: 356,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-blue-800 dark:text-blue-200 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Habilitadas:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \" Aparecem nas configura\\xe7\\xf5es do usu\\xe1rio e podem ser ativadas/desativadas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                            lineNumber: 360,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Obrigat\\xf3rias:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \" Ficam sempre ativas e n\\xe3o podem ser desativadas pelo usu\\xe1rio\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                            lineNumber: 361,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Desabilitadas:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \" N\\xe3o aparecem no sistema e n\\xe3o s\\xe3o enviadas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• As configura\\xe7\\xf5es se aplicam a todos os usu\\xe1rios da empresa selecionada\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                    lineNumber: 359,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                            lineNumber: 355,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 351,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                lineNumber: 350,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\",\n                    onClick: handleSave,\n                    disabled: isSaving,\n                    children: isSaving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 378,\n                                columnNumber: 15\n                            }, this),\n                            \"Salvando...\"\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 383,\n                                columnNumber: 15\n                            }, this),\n                            \"Salvar Prefer\\xeancias\"\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 371,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                lineNumber: 370,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n        lineNumber: 277,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationPreferences, \"F7z/2EI6x6n56HARbqeEka7X2BE=\", false, function() {\n    return [\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = NotificationPreferences;\nvar _c;\n$RefreshReg$(_c, \"NotificationPreferences\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/preferences/NotificationPreferences.js\n"));

/***/ })

});