"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/settings/page",{

/***/ "(app-pages-browser)/./src/components/admin/preferences/NotificationPreferences.js":
/*!*********************************************************************!*\
  !*** ./src/components/admin/preferences/NotificationPreferences.js ***!
  \*********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationPreferences)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.js\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.js\");\n/* harmony import */ var _ui_ModuleCheckbox__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../ui/ModuleCheckbox */ \"(app-pages-browser)/./src/components/ui/ModuleCheckbox.js\");\n/* harmony import */ var _PreferencesSection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./PreferencesSection */ \"(app-pages-browser)/./src/components/admin/preferences/PreferencesSection.js\");\n/* harmony import */ var _services_preferencesService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/services/preferencesService */ \"(app-pages-browser)/./src/services/preferencesService.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-check.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _AppointmentTimingSettings__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./AppointmentTimingSettings */ \"(app-pages-browser)/./src/components/admin/preferences/AppointmentTimingSettings.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Definir tipos de notificação organizados por categoria\nconst NOTIFICATION_CATEGORIES = {\n    appointments: {\n        title: \"Consultas\",\n        description: \"Notificações relacionadas a agendamentos e consultas\",\n        moduleColor: \"scheduler\",\n        notifications: [\n            {\n                key: \"APPOINTMENT_COMING\",\n                label: \"Consultas Chegando\",\n                description: \"Avisos de consultas agendadas (1 hora antes)\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 22,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"NEW_APPOINTMENT_SCHEDULED\",\n                label: \"Nova Consulta Agendada\",\n                description: \"Notificações quando uma nova consulta é agendada no sistema\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 28,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"APPOINTMENT_SCHEDULED_FOR_PROVIDER\",\n                label: \"Consulta Agendada (Profissional)\",\n                description: \"Notificar profissionais quando consultas são agendadas para eles\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 34,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"APPOINTMENT_SCHEDULED_FOR_PATIENT\",\n                label: \"Consulta Agendada (Paciente)\",\n                description: \"Notificar pacientes/clientes quando consultas são agendadas\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 40,\n                    columnNumber: 15\n                }, undefined)\n            }\n        ]\n    },\n    requests: {\n        title: \"Solicitações\",\n        description: \"Notificações sobre solicitações de agendamento\",\n        moduleColor: \"scheduler\",\n        notifications: [\n            {\n                key: \"NEW_APPOINTMENT_REQUEST\",\n                label: \"Nova Solicitação de Agendamento\",\n                description: \"Notificações quando clientes criam novas solicitações de agendamento\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 53,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"APPOINTMENT_REQUEST_APPROVED\",\n                label: \"Solicitação Aprovada\",\n                description: \"Notificar clientes quando suas solicitações são aprovadas\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 59,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"APPOINTMENT_REQUEST_SUGGESTED\",\n                label: \"Sugestão de Alteração\",\n                description: \"Notificar clientes quando há sugestões de alteração em suas solicitações\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 65,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"APPOINTMENT_REQUEST_REJECTED\",\n                label: \"Solicitação Rejeitada\",\n                description: \"Notificar clientes quando suas solicitações são rejeitadas\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 71,\n                    columnNumber: 15\n                }, undefined)\n            }\n        ]\n    },\n    registrations: {\n        title: \"Novos Registros\",\n        description: \"Notificações sobre novos cadastros e acessos\",\n        moduleColor: \"admin\",\n        notifications: [\n            {\n                key: \"NEW_REGISTRATION\",\n                label: \"Novos Cadastros\",\n                description: \"Notificações sobre novos cadastros no sistema\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 84,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"NEW_ACCESS\",\n                label: \"Novos Acessos\",\n                description: \"Notificações sobre novos acessos ao sistema\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 90,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"NEW_BACKUP\",\n                label: \"Novo Backup\",\n                description: \"Confirmação de backups realizados\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 96,\n                    columnNumber: 15\n                }, undefined)\n            }\n        ]\n    },\n    documents: {\n        title: \"Documentos/Exportações\",\n        description: \"Notificações sobre documentos e exportações\",\n        moduleColor: \"people\",\n        notifications: [\n            {\n                key: \"DOCUMENT_SHARED\",\n                label: \"Documentos Compartilhados\",\n                description: \"Notificações quando documentos são compartilhados com usuários ou clientes\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 109,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"NEW_EXPORT\",\n                label: \"Exportações\",\n                description: \"Notificações sobre exportações concluídas\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 115,\n                    columnNumber: 15\n                }, undefined)\n            }\n        ]\n    }\n};\n// Função para obter todas as notificações em formato plano (para compatibilidade)\nconst getAllNotifications = ()=>{\n    const allNotifications = [];\n    Object.values(NOTIFICATION_CATEGORIES).forEach((category)=>{\n        allNotifications.push(...category.notifications);\n    });\n    return allNotifications;\n};\nconst NOTIFICATION_TYPES = getAllNotifications();\n// Função para obter as cores do ícone baseado no módulo\nconst getIconColors = (moduleColor)=>{\n    const colorMap = {\n        scheduler: {\n            text: \"text-purple-600 dark:text-purple-400\"\n        },\n        people: {\n            text: \"text-orange-600 dark:text-orange-400\"\n        },\n        admin: {\n            text: \"text-gray-600 dark:text-gray-400\"\n        },\n        default: {\n            text: \"text-cyan-600 dark:text-cyan-400\"\n        }\n    };\n    return colorMap[moduleColor] || colorMap.default;\n};\nfunction NotificationPreferences(param) {\n    let { search = \"\", searchMode = false, preferences = null, selectedCompanyId = null, onSave = null } = param;\n    _s();\n    // Estados para preferências de notificação\n    const [enabledNotifications, setEnabledNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [requiredNotifications, setRequiredNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [appointmentTimings, setAppointmentTimings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    // Carregar preferências quando o componente receber os dados\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationPreferences.useEffect\": ()=>{\n            if (preferences && preferences.notifications) {\n                setEnabledNotifications(preferences.notifications.enabled || {});\n                setRequiredNotifications(preferences.notifications.required || {});\n                setAppointmentTimings(preferences.notifications.timings || {});\n            } else {\n                // Definir valores padrão se não houver preferências\n                const defaultEnabled = {};\n                const defaultRequired = {};\n                const defaultTimings = {};\n                NOTIFICATION_TYPES.forEach({\n                    \"NotificationPreferences.useEffect\": (type)=>{\n                        defaultEnabled[type.key] = false;\n                        defaultRequired[type.key] = false;\n                        // Valor padrão para APPOINTMENT_COMING: 1 hora antes\n                        if (type.key === 'APPOINTMENT_COMING') {\n                            defaultTimings[type.key] = [\n                                {\n                                    value: 1,\n                                    unit: 'hours'\n                                }\n                            ];\n                        }\n                    }\n                }[\"NotificationPreferences.useEffect\"]);\n                setEnabledNotifications(defaultEnabled);\n                setRequiredNotifications(defaultRequired);\n                setAppointmentTimings(defaultTimings);\n            }\n        }\n    }[\"NotificationPreferences.useEffect\"], [\n        preferences\n    ]);\n    // Filtrar categorias baseado na pesquisa\n    const filteredCategories = searchMode && search ? Object.entries(NOTIFICATION_CATEGORIES).reduce((acc, param)=>{\n        let [key, category] = param;\n        const filteredNotifications = category.notifications.filter((notification)=>notification.label.toLowerCase().includes(search.toLowerCase()) || notification.description.toLowerCase().includes(search.toLowerCase()));\n        if (filteredNotifications.length > 0) {\n            acc[key] = {\n                ...category,\n                notifications: filteredNotifications\n            };\n        }\n        return acc;\n    }, {}) : NOTIFICATION_CATEGORIES;\n    // Manter compatibilidade com filteredNotifications para o resto do código\n    const filteredNotifications = searchMode && search ? NOTIFICATION_TYPES.filter((notification)=>notification.label.toLowerCase().includes(search.toLowerCase()) || notification.description.toLowerCase().includes(search.toLowerCase())) : NOTIFICATION_TYPES;\n    // Se não há resultados na pesquisa, não mostrar nada\n    if (searchMode && Object.keys(filteredCategories).length === 0) {\n        return null;\n    }\n    // Função para toggle de notificação habilitada\n    const toggleNotificationEnabled = (key)=>{\n        setEnabledNotifications((prev)=>({\n                ...prev,\n                [key]: !prev[key]\n            }));\n        // Se desabilitar, também desabilitar obrigatoriedade\n        if (enabledNotifications[key]) {\n            setRequiredNotifications((prev)=>({\n                    ...prev,\n                    [key]: false\n                }));\n        }\n    };\n    // Função para toggle de obrigatoriedade\n    const toggleNotificationRequired = (key)=>{\n        setRequiredNotifications((prev)=>({\n                ...prev,\n                [key]: !prev[key]\n            }));\n    };\n    // Função para atualizar timings de uma notificação específica\n    const handleTimingChange = (notificationKey, timings)=>{\n        setAppointmentTimings((prev)=>({\n                ...prev,\n                [notificationKey]: timings\n            }));\n    };\n    // Função para salvar preferências\n    const handleSave = async ()=>{\n        setIsSaving(true);\n        try {\n            const notificationPreferences = {\n                enabled: enabledNotifications,\n                required: requiredNotifications,\n                timings: appointmentTimings\n            };\n            // Manter as preferências existentes e adicionar as de notificação\n            const currentPreferences = preferences || {};\n            const updatedPreferences = {\n                ...currentPreferences,\n                notifications: notificationPreferences\n            };\n            if (onSave) {\n                // Usar função de salvamento fornecida pelo pai (suporta empresas específicas)\n                const success = await onSave(updatedPreferences);\n                if (success) {\n                    toast_success(\"Preferências de notificação salvas com sucesso!\");\n                }\n            } else {\n                toast_error(\"Erro: função de salvamento não fornecida\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao salvar preferências de notificação:\", error);\n            toast_error(\"Erro ao salvar preferências de notificação\");\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    if (searchMode && filteredNotifications.length === 0) {\n        return null;\n    }\n    const showNotifications = !searchMode || filteredNotifications.length > 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            showNotifications && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-6 bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-4\",\n                        children: \"Prefer\\xeancias de Notifica\\xe7\\xe3o\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                        lineNumber: 297,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-neutral-600 dark:text-neutral-300 mb-6\",\n                        children: [\n                            \"Configure quais tipos de notifica\\xe7\\xe3o estar\\xe3o dispon\\xedveis no sistema. Notifica\\xe7\\xf5es \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-blue-600 dark:text-blue-400\",\n                                children: \"habilitadas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 298,\n                                columnNumber: 160\n                            }, this),\n                            \" aparecer\\xe3o para os usu\\xe1rios. Notifica\\xe7\\xf5es \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-purple-600 dark:text-purple-400\",\n                                children: \"obrigat\\xf3rias\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 298,\n                                columnNumber: 286\n                            }, this),\n                            \" n\\xe3o podem ser desativadas pelos usu\\xe1rios.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                        lineNumber: 298,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: Object.entries(filteredCategories).map((param)=>{\n                            let [categoryKey, category] = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3 pb-2 border-b border-gray-200 dark:border-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"\".concat(getIconColors(category.moduleColor).text),\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                lineNumber: 305,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                        children: category.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                        children: category.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                lineNumber: 306,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                        lineNumber: 304,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-4\",\n                                        children: category.notifications.map((notification)=>{\n                                            const iconColors = getIconColors(category.moduleColor);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start gap-3 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"\".concat(iconColors.text),\n                                                                children: notification.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                                        children: notification.label\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                                        lineNumber: 327,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                                                                        children: notification.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ModuleCheckbox__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                id: \"enabled-\".concat(notification.key),\n                                                                checked: enabledNotifications[notification.key] || false,\n                                                                onChange: ()=>toggleNotificationEnabled(notification.key),\n                                                                disabled: isSaving,\n                                                                label: \"Habilitada no sistema\",\n                                                                moduleColor: \"admin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ModuleCheckbox__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                id: \"required-\".concat(notification.key),\n                                                                checked: requiredNotifications[notification.key] || false,\n                                                                onChange: ()=>toggleNotificationRequired(notification.key),\n                                                                disabled: !enabledNotifications[notification.key] || isSaving,\n                                                                label: \"Obrigat\\xf3ria para todos os usu\\xe1rios\",\n                                                                moduleColor: \"admin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            notification.key === 'APPOINTMENT_COMING' && enabledNotifications[notification.key] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 pt-3 border-t border-gray-200 dark:border-gray-600\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AppointmentTimingSettings__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    timings: appointmentTimings[notification.key] || [\n                                                                        {\n                                                                            value: 1,\n                                                                            unit: 'hours'\n                                                                        }\n                                                                    ],\n                                                                    onChange: (timings)=>handleTimingChange(notification.key, timings),\n                                                                    disabled: isSaving\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                                    lineNumber: 358,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, notification.key, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                lineNumber: 321,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                        lineNumber: 317,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, categoryKey, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 302,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                lineNumber: 296,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                className: \"text-blue-600 dark:text-blue-400\",\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 380,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                            lineNumber: 379,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-blue-900 dark:text-blue-100 mb-2\",\n                                    children: \"Como funcionam as notifica\\xe7\\xf5es\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                    lineNumber: 383,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-blue-800 dark:text-blue-200 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Habilitadas:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \" Aparecem nas configura\\xe7\\xf5es do usu\\xe1rio e podem ser ativadas/desativadas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                            lineNumber: 387,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Obrigat\\xf3rias:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \" Ficam sempre ativas e n\\xe3o podem ser desativadas pelo usu\\xe1rio\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                            lineNumber: 388,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Desabilitadas:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \" N\\xe3o aparecem no sistema e n\\xe3o s\\xe3o enviadas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                            lineNumber: 389,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• As configura\\xe7\\xf5es se aplicam a todos os usu\\xe1rios da empresa selecionada\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                            lineNumber: 390,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                    lineNumber: 386,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                            lineNumber: 382,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 378,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                lineNumber: 377,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\",\n                    onClick: handleSave,\n                    disabled: isSaving,\n                    children: isSaving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 405,\n                                columnNumber: 15\n                            }, this),\n                            \"Salvando...\"\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 410,\n                                columnNumber: 15\n                            }, this),\n                            \"Salvar Prefer\\xeancias\"\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 398,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                lineNumber: 397,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n        lineNumber: 293,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationPreferences, \"F7z/2EI6x6n56HARbqeEka7X2BE=\", false, function() {\n    return [\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = NotificationPreferences;\nvar _c;\n$RefreshReg$(_c, \"NotificationPreferences\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/preferences/NotificationPreferences.js\n"));

/***/ })

});