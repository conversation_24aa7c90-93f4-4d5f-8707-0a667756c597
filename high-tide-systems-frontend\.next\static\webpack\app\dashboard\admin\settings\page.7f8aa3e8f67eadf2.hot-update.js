"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/settings/page",{

/***/ "(app-pages-browser)/./src/components/admin/preferences/AppointmentTimingSettings.js":
/*!***********************************************************************!*\
  !*** ./src/components/admin/preferences/AppointmentTimingSettings.js ***!
  \***********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AppointmentTimingSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.js\");\n/* harmony import */ var _ui_ModuleCheckbox__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../ui/ModuleCheckbox */ \"(app-pages-browser)/./src/components/ui/ModuleCheckbox.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n// Opções predefinidas de timing\nconst PREDEFINED_TIMINGS = [\n    {\n        value: 15,\n        unit: 'minutes',\n        label: '15 minutos antes'\n    },\n    {\n        value: 30,\n        unit: 'minutes',\n        label: '30 minutos antes'\n    },\n    {\n        value: 1,\n        unit: 'hours',\n        label: '1 hora antes'\n    },\n    {\n        value: 2,\n        unit: 'hours',\n        label: '2 horas antes'\n    },\n    {\n        value: 4,\n        unit: 'hours',\n        label: '4 horas antes'\n    },\n    {\n        value: 1,\n        unit: 'days',\n        label: '1 dia antes'\n    },\n    {\n        value: 2,\n        unit: 'days',\n        label: '2 dias antes'\n    },\n    {\n        value: 7,\n        unit: 'days',\n        label: '1 semana antes'\n    }\n];\n// Função para converter timing para minutos (para comparação e ordenação)\nconst timingToMinutes = (value, unit)=>{\n    switch(unit){\n        case 'minutes':\n            return value;\n        case 'hours':\n            return value * 60;\n        case 'days':\n            return value * 60 * 24;\n        default:\n            return value;\n    }\n};\n// Função para formatar timing para exibição\nconst formatTiming = (value, unit)=>{\n    if (unit === 'minutes') {\n        return \"\".concat(value, \" minuto\").concat(value !== 1 ? 's' : '', \" antes\");\n    } else if (unit === 'hours') {\n        return \"\".concat(value, \" hora\").concat(value !== 1 ? 's' : '', \" antes\");\n    } else if (unit === 'days') {\n        if (value === 7) return '1 semana antes';\n        return \"\".concat(value, \" dia\").concat(value !== 1 ? 's' : '', \" antes\");\n    }\n    return \"\".concat(value, \" \").concat(unit, \" antes\");\n};\nfunction AppointmentTimingSettings(param) {\n    let { timings = [], onChange, disabled = false } = param;\n    _s();\n    const [selectedTimings, setSelectedTimings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showCustomForm, setShowCustomForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customValue, setCustomValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [customUnit, setCustomUnit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('minutes');\n    // Inicializar com os timings recebidos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentTimingSettings.useEffect\": ()=>{\n            if (timings && timings.length > 0) {\n                setSelectedTimings(timings);\n            } else {\n                // Valor padrão: 1 hora antes\n                setSelectedTimings([\n                    {\n                        value: 1,\n                        unit: 'hours'\n                    }\n                ]);\n            }\n        }\n    }[\"AppointmentTimingSettings.useEffect\"], [\n        timings\n    ]);\n    // Notificar mudanças para o componente pai\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentTimingSettings.useEffect\": ()=>{\n            if (onChange) {\n                onChange(selectedTimings);\n            }\n        }\n    }[\"AppointmentTimingSettings.useEffect\"], [\n        selectedTimings,\n        onChange\n    ]);\n    // Adicionar timing predefinido\n    const addPredefinedTiming = (timing)=>{\n        const exists = selectedTimings.some((t)=>t.value === timing.value && t.unit === timing.unit);\n        if (!exists) {\n            const newTimings = [\n                ...selectedTimings,\n                timing\n            ];\n            // Ordenar por tempo (menor para maior)\n            newTimings.sort((a, b)=>timingToMinutes(a.value, a.unit) - timingToMinutes(b.value, b.unit));\n            setSelectedTimings(newTimings);\n        }\n    };\n    // Adicionar timing personalizado\n    const addCustomTiming = ()=>{\n        const value = parseInt(customValue);\n        if (isNaN(value) || value <= 0) return;\n        const timing = {\n            value,\n            unit: customUnit\n        };\n        const exists = selectedTimings.some((t)=>t.value === timing.value && t.unit === timing.unit);\n        if (!exists) {\n            const newTimings = [\n                ...selectedTimings,\n                timing\n            ];\n            // Ordenar por tempo (menor para maior)\n            newTimings.sort((a, b)=>timingToMinutes(a.value, a.unit) - timingToMinutes(b.value, b.unit));\n            setSelectedTimings(newTimings);\n        }\n        // Limpar formulário\n        setCustomValue('');\n        setCustomUnit('minutes');\n        setShowCustomForm(false);\n    };\n    // Remover timing\n    const removeTiming = (index)=>{\n        const newTimings = selectedTimings.filter((_, i)=>i !== index);\n        setSelectedTimings(newTimings);\n    };\n    // Verificar se um timing predefinido já está selecionado\n    const isPredefinedSelected = (timing)=>{\n        return selectedTimings.some((t)=>t.value === timing.value && t.unit === timing.unit);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2 mb-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"text-purple-600 dark:text-purple-400\",\n                        size: 16\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                        className: \"font-medium text-gray-900 dark:text-white\",\n                        children: \"Hor\\xe1rios de Notifica\\xe7\\xe3o\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            selectedTimings.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                        children: \"Hor\\xe1rios configurados:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2\",\n                        children: selectedTimings.map((timing, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        size: 12\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                                        lineNumber: 139,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: formatTiming(timing.value, timing.unit)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                                        lineNumber: 140,\n                                        columnNumber: 17\n                                    }, this),\n                                    !disabled && selectedTimings.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>removeTiming(index),\n                                        className: \"text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            size: 12\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                                            lineNumber: 146,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                                        lineNumber: 142,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                                lineNumber: 135,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                lineNumber: 129,\n                columnNumber: 9\n            }, this),\n            !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                children: \"Op\\xe7\\xf5es r\\xe1pidas:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 sm:grid-cols-4 gap-2\",\n                                children: PREDEFINED_TIMINGS.map((timing, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>addPredefinedTiming(timing),\n                                        disabled: isPredefinedSelected(timing),\n                                        className: \"p-2 text-xs rounded-lg border transition-colors \".concat(isPredefinedSelected(timing) ? 'bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 border-gray-200 dark:border-gray-600 cursor-not-allowed' : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-600 hover:bg-purple-50 dark:hover:bg-purple-900/20 hover:border-purple-300 dark:hover:border-purple-600'),\n                                        children: timing.label\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                                        lineNumber: 164,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this),\n                    !showCustomForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowCustomForm(true),\n                        className: \"flex items-center gap-2 text-sm text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                size: 14\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                                lineNumber: 186,\n                                columnNumber: 15\n                            }, this),\n                            \"Adicionar hor\\xe1rio personalizado\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                        lineNumber: 182,\n                        columnNumber: 13\n                    }, this),\n                    showCustomForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"p-4 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                    children: \"Adicionar hor\\xe1rio personalizado:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                                    lineNumber: 195,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            min: \"1\",\n                                            value: customValue,\n                                            onChange: (e)=>setCustomValue(e.target.value),\n                                            placeholder: \"Valor\",\n                                            className: \"flex-1 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                                            lineNumber: 199,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: customUnit,\n                                            onChange: (e)=>setCustomUnit(e.target.value),\n                                            className: \"px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"minutes\",\n                                                    children: \"Minutos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"hours\",\n                                                    children: \"Horas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"days\",\n                                                    children: \"Dias\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                                            lineNumber: 207,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                                    lineNumber: 198,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: addCustomTiming,\n                                            disabled: !customValue || parseInt(customValue) <= 0,\n                                            className: \"px-3 py-2 text-sm bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: \"Adicionar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                                            lineNumber: 218,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowCustomForm(false);\n                                                setCustomValue('');\n                                                setCustomUnit('minutes');\n                                            },\n                                            className: \"px-3 py-2 text-sm bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500\",\n                                            children: \"Cancelar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                                            lineNumber: 225,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                                    lineNumber: 217,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                            lineNumber: 194,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                        lineNumber: 193,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 p-3 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-medium mb-1\",\n                        children: \"Como funciona:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• As notifica\\xe7\\xf5es ser\\xe3o enviadas nos hor\\xe1rios configurados antes da consulta\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Voc\\xea pode ter m\\xfaltiplos hor\\xe1rios (ex: 1 dia antes + 1 hora antes)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Os hor\\xe1rios s\\xe3o ordenados automaticamente do menor para o maior\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\AppointmentTimingSettings.js\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n_s(AppointmentTimingSettings, \"MndTAzAxtsvoY6qQY5lV7ZrwzsA=\");\n_c = AppointmentTimingSettings;\nvar _c;\n$RefreshReg$(_c, \"AppointmentTimingSettings\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/preferences/AppointmentTimingSettings.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/admin/preferences/NotificationPreferences.js":
/*!*********************************************************************!*\
  !*** ./src/components/admin/preferences/NotificationPreferences.js ***!
  \*********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationPreferences)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.js\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.js\");\n/* harmony import */ var _ui_ModuleCheckbox__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../ui/ModuleCheckbox */ \"(app-pages-browser)/./src/components/ui/ModuleCheckbox.js\");\n/* harmony import */ var _PreferencesSection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./PreferencesSection */ \"(app-pages-browser)/./src/components/admin/preferences/PreferencesSection.js\");\n/* harmony import */ var _services_preferencesService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/services/preferencesService */ \"(app-pages-browser)/./src/services/preferencesService.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-check.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Calendar,ClipboardCheck,Clock,Database,Download,FileText,Plus,Settings,Shield,UserCheck,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _AppointmentTimingSettings__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./AppointmentTimingSettings */ \"(app-pages-browser)/./src/components/admin/preferences/AppointmentTimingSettings.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Definir tipos de notificação organizados por categoria\nconst NOTIFICATION_CATEGORIES = {\n    appointments: {\n        title: \"Consultas\",\n        description: \"Notificações relacionadas a agendamentos e consultas\",\n        moduleColor: \"scheduler\",\n        notifications: [\n            {\n                key: \"APPOINTMENT_COMING\",\n                label: \"Consultas Chegando\",\n                description: \"Avisos de consultas agendadas (1 hora antes)\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 22,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"NEW_APPOINTMENT_SCHEDULED\",\n                label: \"Nova Consulta Agendada\",\n                description: \"Notificações quando uma nova consulta é agendada no sistema\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 28,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"APPOINTMENT_SCHEDULED_FOR_PROVIDER\",\n                label: \"Consulta Agendada (Profissional)\",\n                description: \"Notificar profissionais quando consultas são agendadas para eles\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 34,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"APPOINTMENT_SCHEDULED_FOR_PATIENT\",\n                label: \"Consulta Agendada (Paciente)\",\n                description: \"Notificar pacientes/clientes quando consultas são agendadas\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 40,\n                    columnNumber: 15\n                }, undefined)\n            }\n        ]\n    },\n    requests: {\n        title: \"Solicitações\",\n        description: \"Notificações sobre solicitações de agendamento\",\n        moduleColor: \"scheduler\",\n        notifications: [\n            {\n                key: \"NEW_APPOINTMENT_REQUEST\",\n                label: \"Nova Solicitação de Agendamento\",\n                description: \"Notificações quando clientes criam novas solicitações de agendamento\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 53,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"APPOINTMENT_REQUEST_APPROVED\",\n                label: \"Solicitação Aprovada\",\n                description: \"Notificar clientes quando suas solicitações são aprovadas\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 59,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"APPOINTMENT_REQUEST_SUGGESTED\",\n                label: \"Sugestão de Alteração\",\n                description: \"Notificar clientes quando há sugestões de alteração em suas solicitações\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 65,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"APPOINTMENT_REQUEST_REJECTED\",\n                label: \"Solicitação Rejeitada\",\n                description: \"Notificar clientes quando suas solicitações são rejeitadas\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 71,\n                    columnNumber: 15\n                }, undefined)\n            }\n        ]\n    },\n    registrations: {\n        title: \"Novos Registros\",\n        description: \"Notificações sobre novos cadastros e acessos\",\n        moduleColor: \"admin\",\n        notifications: [\n            {\n                key: \"NEW_REGISTRATION\",\n                label: \"Novos Cadastros\",\n                description: \"Notificações sobre novos cadastros no sistema\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 84,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"NEW_ACCESS\",\n                label: \"Novos Acessos\",\n                description: \"Notificações sobre novos acessos ao sistema\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 90,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"NEW_BACKUP\",\n                label: \"Novo Backup\",\n                description: \"Confirmação de backups realizados\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 96,\n                    columnNumber: 15\n                }, undefined)\n            }\n        ]\n    },\n    documents: {\n        title: \"Documentos/Exportações\",\n        description: \"Notificações sobre documentos e exportações\",\n        moduleColor: \"people\",\n        notifications: [\n            {\n                key: \"DOCUMENT_SHARED\",\n                label: \"Documentos Compartilhados\",\n                description: \"Notificações quando documentos são compartilhados com usuários ou clientes\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 109,\n                    columnNumber: 15\n                }, undefined)\n            },\n            {\n                key: \"NEW_EXPORT\",\n                label: \"Exportações\",\n                description: \"Notificações sobre exportações concluídas\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 115,\n                    columnNumber: 15\n                }, undefined)\n            }\n        ]\n    }\n};\n// Função para obter todas as notificações em formato plano (para compatibilidade)\nconst getAllNotifications = ()=>{\n    const allNotifications = [];\n    Object.values(NOTIFICATION_CATEGORIES).forEach((category)=>{\n        allNotifications.push(...category.notifications);\n    });\n    return allNotifications;\n};\nconst NOTIFICATION_TYPES = getAllNotifications();\n// Função para obter as cores do ícone baseado no módulo\nconst getIconColors = (moduleColor)=>{\n    const colorMap = {\n        scheduler: {\n            text: \"text-purple-600 dark:text-purple-400\"\n        },\n        people: {\n            text: \"text-orange-600 dark:text-orange-400\"\n        },\n        admin: {\n            text: \"text-gray-600 dark:text-gray-400\"\n        },\n        default: {\n            text: \"text-cyan-600 dark:text-cyan-400\"\n        }\n    };\n    return colorMap[moduleColor] || colorMap.default;\n};\nfunction NotificationPreferences(param) {\n    let { search = \"\", searchMode = false, preferences = null, selectedCompanyId = null, onSave = null } = param;\n    _s();\n    // Estados para preferências de notificação\n    const [enabledNotifications, setEnabledNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [requiredNotifications, setRequiredNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    // Carregar preferências quando o componente receber os dados\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationPreferences.useEffect\": ()=>{\n            if (preferences && preferences.notifications) {\n                setEnabledNotifications(preferences.notifications.enabled || {});\n                setRequiredNotifications(preferences.notifications.required || {});\n            } else {\n                // Definir valores padrão se não houver preferências\n                const defaultEnabled = {};\n                const defaultRequired = {};\n                NOTIFICATION_TYPES.forEach({\n                    \"NotificationPreferences.useEffect\": (type)=>{\n                        defaultEnabled[type.key] = false;\n                        defaultRequired[type.key] = false;\n                    }\n                }[\"NotificationPreferences.useEffect\"]);\n                setEnabledNotifications(defaultEnabled);\n                setRequiredNotifications(defaultRequired);\n            }\n        }\n    }[\"NotificationPreferences.useEffect\"], [\n        preferences\n    ]);\n    // Filtrar categorias baseado na pesquisa\n    const filteredCategories = searchMode && search ? Object.entries(NOTIFICATION_CATEGORIES).reduce((acc, param)=>{\n        let [key, category] = param;\n        const filteredNotifications = category.notifications.filter((notification)=>notification.label.toLowerCase().includes(search.toLowerCase()) || notification.description.toLowerCase().includes(search.toLowerCase()));\n        if (filteredNotifications.length > 0) {\n            acc[key] = {\n                ...category,\n                notifications: filteredNotifications\n            };\n        }\n        return acc;\n    }, {}) : NOTIFICATION_CATEGORIES;\n    // Manter compatibilidade com filteredNotifications para o resto do código\n    const filteredNotifications = searchMode && search ? NOTIFICATION_TYPES.filter((notification)=>notification.label.toLowerCase().includes(search.toLowerCase()) || notification.description.toLowerCase().includes(search.toLowerCase())) : NOTIFICATION_TYPES;\n    // Se não há resultados na pesquisa, não mostrar nada\n    if (searchMode && Object.keys(filteredCategories).length === 0) {\n        return null;\n    }\n    // Função para toggle de notificação habilitada\n    const toggleNotificationEnabled = (key)=>{\n        setEnabledNotifications((prev)=>({\n                ...prev,\n                [key]: !prev[key]\n            }));\n        // Se desabilitar, também desabilitar obrigatoriedade\n        if (enabledNotifications[key]) {\n            setRequiredNotifications((prev)=>({\n                    ...prev,\n                    [key]: false\n                }));\n        }\n    };\n    // Função para toggle de obrigatoriedade\n    const toggleNotificationRequired = (key)=>{\n        setRequiredNotifications((prev)=>({\n                ...prev,\n                [key]: !prev[key]\n            }));\n    };\n    // Função para salvar preferências\n    const handleSave = async ()=>{\n        setIsSaving(true);\n        try {\n            const notificationPreferences = {\n                enabled: enabledNotifications,\n                required: requiredNotifications\n            };\n            // Manter as preferências existentes e adicionar as de notificação\n            const currentPreferences = preferences || {};\n            const updatedPreferences = {\n                ...currentPreferences,\n                notifications: notificationPreferences\n            };\n            if (onSave) {\n                // Usar função de salvamento fornecida pelo pai (suporta empresas específicas)\n                const success = await onSave(updatedPreferences);\n                if (success) {\n                    toast_success(\"Preferências de notificação salvas com sucesso!\");\n                }\n            } else {\n                toast_error(\"Erro: função de salvamento não fornecida\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao salvar preferências de notificação:\", error);\n            toast_error(\"Erro ao salvar preferências de notificação\");\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    if (searchMode && filteredNotifications.length === 0) {\n        return null;\n    }\n    const showNotifications = !searchMode || filteredNotifications.length > 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            showNotifications && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-6 bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-4\",\n                        children: \"Prefer\\xeancias de Notifica\\xe7\\xe3o\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                        lineNumber: 280,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-neutral-600 dark:text-neutral-300 mb-6\",\n                        children: [\n                            \"Configure quais tipos de notifica\\xe7\\xe3o estar\\xe3o dispon\\xedveis no sistema. Notifica\\xe7\\xf5es \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-blue-600 dark:text-blue-400\",\n                                children: \"habilitadas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 281,\n                                columnNumber: 160\n                            }, this),\n                            \" aparecer\\xe3o para os usu\\xe1rios. Notifica\\xe7\\xf5es \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-purple-600 dark:text-purple-400\",\n                                children: \"obrigat\\xf3rias\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 281,\n                                columnNumber: 286\n                            }, this),\n                            \" n\\xe3o podem ser desativadas pelos usu\\xe1rios.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                        lineNumber: 281,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: Object.entries(filteredCategories).map((param)=>{\n                            let [categoryKey, category] = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3 pb-2 border-b border-gray-200 dark:border-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"\".concat(getIconColors(category.moduleColor).text),\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                lineNumber: 288,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                        children: category.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                        children: category.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                        lineNumber: 287,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-4\",\n                                        children: category.notifications.map((notification)=>{\n                                            const iconColors = getIconColors(category.moduleColor);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start gap-3 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"\".concat(iconColors.text),\n                                                                children: notification.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                                        children: notification.label\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                                        lineNumber: 310,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                                                                        children: notification.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ModuleCheckbox__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                id: \"enabled-\".concat(notification.key),\n                                                                checked: enabledNotifications[notification.key] || false,\n                                                                onChange: ()=>toggleNotificationEnabled(notification.key),\n                                                                disabled: isSaving,\n                                                                label: \"Habilitada no sistema\",\n                                                                moduleColor: \"admin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ModuleCheckbox__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                id: \"required-\".concat(notification.key),\n                                                                checked: requiredNotifications[notification.key] || false,\n                                                                onChange: ()=>toggleNotificationRequired(notification.key),\n                                                                disabled: !enabledNotifications[notification.key] || isSaving,\n                                                                label: \"Obrigat\\xf3ria para todos os usu\\xe1rios\",\n                                                                moduleColor: \"admin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, notification.key, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                lineNumber: 304,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                        lineNumber: 300,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, categoryKey, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 285,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                        lineNumber: 283,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                lineNumber: 279,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                className: \"text-blue-600 dark:text-blue-400\",\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-blue-900 dark:text-blue-100 mb-2\",\n                                    children: \"Como funcionam as notifica\\xe7\\xf5es\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                    lineNumber: 355,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-blue-800 dark:text-blue-200 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Habilitadas:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \" Aparecem nas configura\\xe7\\xf5es do usu\\xe1rio e podem ser ativadas/desativadas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                            lineNumber: 359,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Obrigat\\xf3rias:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \" Ficam sempre ativas e n\\xe3o podem ser desativadas pelo usu\\xe1rio\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                            lineNumber: 360,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: [\n                                                \"• \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Desabilitadas:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \" N\\xe3o aparecem no sistema e n\\xe3o s\\xe3o enviadas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                            lineNumber: 361,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• As configura\\xe7\\xf5es se aplicam a todos os usu\\xe1rios da empresa selecionada\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                            lineNumber: 354,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 350,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                lineNumber: 349,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\",\n                    onClick: handleSave,\n                    disabled: isSaving,\n                    children: isSaving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 377,\n                                columnNumber: 15\n                            }, this),\n                            \"Salvando...\"\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Calendar_ClipboardCheck_Clock_Database_Download_FileText_Plus_Settings_Shield_UserCheck_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                                lineNumber: 382,\n                                columnNumber: 15\n                            }, this),\n                            \"Salvar Prefer\\xeancias\"\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                    lineNumber: 370,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n                lineNumber: 369,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\preferences\\\\NotificationPreferences.js\",\n        lineNumber: 276,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationPreferences, \"JlajU5b7Eh4+ce/2mDsMkIE7bNk=\", false, function() {\n    return [\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = NotificationPreferences;\nvar _c;\n$RefreshReg$(_c, \"NotificationPreferences\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/preferences/NotificationPreferences.js\n"));

/***/ })

});