const NotificationPermissionService = require('../services/notificationPermissionService');

class NotificationPermissionController {
  /**
   * Obtém as permissões de notificação de um usuário
   */
  static async getUserNotificationPermissions(req, res) {
    try {
      const { userId } = req.params;
      const { companyId: requestedCompanyId } = req.query;
      
      // Para SYSTEM_ADMIN, permitir especificar empresa via query parameter
      let companyId = req.user.companyId;
      if (req.user.role === 'SYSTEM_ADMIN' && requestedCompanyId) {
        companyId = requestedCompanyId;
      }

      // Verificar se o usuário tem permissão para ver as permissões
      if (req.user.role !== 'SYSTEM_ADMIN' && req.user.role !== 'COMPANY_ADMIN' && req.user.id !== userId) {
        return res.status(403).json({ message: 'Acesso negado' });
      }

      const permissions = await NotificationPermissionService.getUserNotificationPermissions(userId, companyId);
      
      res.json({
        data: permissions,
        success: true
      });
    } catch (error) {
      console.error('Erro ao obter permissões de notificação:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Atualiza as permissões de notificação de um usuário
   */
  static async updateUserNotificationPermissions(req, res) {
    try {
      const { userId } = req.params;
      const { companyId: requestedCompanyId, ...permissions } = req.body;
      
      // Para SYSTEM_ADMIN, permitir especificar empresa via body
      let companyId = req.user.companyId;
      if (req.user.role === 'SYSTEM_ADMIN' && requestedCompanyId) {
        companyId = requestedCompanyId;
      }

      // Verificar se o usuário tem permissão para alterar as permissões
      if (req.user.role !== 'SYSTEM_ADMIN' && req.user.role !== 'COMPANY_ADMIN') {
        return res.status(403).json({ message: 'Acesso negado' });
      }

      // Verificar se o usuário tem permissão específica para gerenciar permissões de notificação
      if (req.user.role === 'COMPANY_ADMIN' && !req.user.permissions?.includes('admin.notifications.permissions.manage')) {
        return res.status(403).json({ message: 'Você não tem permissão para gerenciar permissões de notificação' });
      }

      const success = await NotificationPermissionService.updateUserNotificationPermissions(userId, companyId, permissions);
      
      if (success) {
        res.json({
          message: 'Permissões de notificação atualizadas com sucesso',
          success: true
        });
      } else {
        res.status(400).json({ message: 'Erro ao atualizar permissões' });
      }
    } catch (error) {
      console.error('Erro ao atualizar permissões de notificação:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Remove todas as permissões de notificação de um usuário
   */
  static async removeAllNotificationPermissions(req, res) {
    try {
      const { userId } = req.params;

      // Verificar se o usuário tem permissão para remover permissões
      if (req.user.role !== 'SYSTEM_ADMIN' && req.user.role !== 'COMPANY_ADMIN') {
        return res.status(403).json({ message: 'Acesso negado' });
      }

      // Verificar se o usuário tem permissão específica para gerenciar permissões de notificação
      if (req.user.role === 'COMPANY_ADMIN' && !req.user.permissions?.includes('admin.notifications.permissions.manage')) {
        return res.status(403).json({ message: 'Você não tem permissão para gerenciar permissões de notificação' });
      }

      const success = await NotificationPermissionService.removeAllNotificationPermissions(userId);
      
      if (success) {
        res.json({
          message: 'Todas as permissões de notificação foram removidas',
          success: true
        });
      } else {
        res.status(400).json({ message: 'Erro ao remover permissões' });
      }
    } catch (error) {
      console.error('Erro ao remover permissões de notificação:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Obtém todas as permissões de notificação disponíveis
   */
  static async getAvailableNotificationPermissions(req, res) {
    try {
      const permissions = {
        NEW_REGISTRATION: {
          name: 'Novos Cadastros',
          description: 'Notificações sobre novos cadastros no sistema'
        },
        APPOINTMENT_COMING: {
          name: 'Consultas Chegando',
          description: 'Avisos de consultas agendadas com horários configuráveis'
        },
        NEW_ACCESS: {
          name: 'Novos Acessos',
          description: 'Notificações sobre novos acessos ao sistema'
        },
        NEW_BACKUP: {
          name: 'Novo Backup',
          description: 'Confirmação de backups realizados'
        },
        NEW_EXPORT: {
          name: 'Exportações',
          description: 'Notificações sobre exportações concluídas'
        },
        SYSTEM_ALERT: {
          name: 'Alertas do Sistema',
          description: 'Alertas importantes do sistema'
        }
      };

      res.json({
        data: permissions,
        success: true
      });
    } catch (error) {
      console.error('Erro ao obter permissões disponíveis:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Obtém todas as empresas disponíveis (apenas para SYSTEM_ADMIN)
   */
  static async getAvailableCompanies(req, res) {
    try {
      // Verificar se é SYSTEM_ADMIN
      if (req.user.role !== 'SYSTEM_ADMIN') {
        return res.status(403).json({ message: 'Acesso restrito a administradores do sistema' });
      }

      const prisma = require('../utils/prisma');
      const companies = await prisma.company.findMany({
        where: {
          active: true,
          deletedAt: null
        },
        select: {
          id: true,
          name: true,
          active: true
        },
        orderBy: {
          name: 'asc'
        }
      });

      res.json({
        data: companies,
        success: true
      });
    } catch (error) {
      console.error('Erro ao obter empresas disponíveis:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }
}

module.exports = {
  NotificationPermissionController
};
