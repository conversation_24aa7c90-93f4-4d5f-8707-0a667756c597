"use client";

import React, { useState, useEffect } from "react";
import { Bell, UserPlus, Clock, Shield, Database, Download, Info } from "lucide-react";
import { ModuleFormGroup, ModuleCheckbox } from "@/components/ui";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/contexts/ToastContext";
import { api } from "@/utils/api";

// Definir tipos de notificação
const NOTIFICATION_TYPES = [
  { 
    key: "NEW_REGISTRATION", 
    label: "Novos Cadastros",
    description: "Receber notificações sobre novos cadastros no sistema",
    icon: <UserPlus size={16} />
  },
  {
    key: "APPOINTMENT_COMING",
    label: "Consultas Chegando",
    description: "Receber avisos de consultas agendadas com horários configuráveis",
    icon: <Clock size={16} />
  },
  { 
    key: "NEW_APPOINTMENT_SCHEDULED", 
    label: "Novos Agendamentos",
    description: "Receber notificações sobre novos agendamentos criados",
    icon: <Clock size={16} />
  },
  { 
    key: "APPOINTMENT_SCHEDULED_FOR_PROVIDER", 
    label: "Agendamentos para Mim",
    description: "Receber notificações quando agendamentos forem marcados para você",
    icon: <Clock size={16} />
  },
  { 
    key: "DOCUMENT_SHARED", 
    label: "Documentos Compartilhados",
    description: "Receber notificações quando documentos forem compartilhados com você",
    icon: <Download size={16} />
  },
  { 
    key: "NEW_ACCESS", 
    label: "Novos Acessos",
    description: "Receber notificações sobre novos acessos ao sistema",
    icon: <Shield size={16} />
  }
];

const UserNotificationPreferences = () => {
  const { user } = useAuth();
  const { toast_success, toast_error } = useToast();
  const [preferences, setPreferences] = useState({});
  const [companyPreferences, setCompanyPreferences] = useState({});
  const [userPermissions, setUserPermissions] = useState({});
  const [isLoading, setIsLoading] = useState(true);

  // Carregar preferências do usuário e da empresa
  useEffect(() => {
    if (user) {
      loadPreferences();
    }
  }, [user]);

  const loadPreferences = async () => {
    try {
      setIsLoading(true);

      console.log('Carregando preferências de notificação...');
      console.log('Usuário atual:', user);

      // Carregar preferências do usuário
      const userPrefsResponse = await api.get('/notifications/user-preferences');
      console.log('Resposta preferências do usuário:', userPrefsResponse.data);
      const userPrefs = userPrefsResponse.data.data || {};
      const userPerms = userPrefsResponse.data.permissions || {};

      // Carregar preferências da empresa
      const companyPrefsResponse = await api.get('/notifications/company-preferences');
      console.log('Resposta preferências da empresa:', companyPrefsResponse.data);
      const companyPrefs = companyPrefsResponse.data.data || {};

      // Processar preferências do usuário
      const userNotificationPrefs = {};
      NOTIFICATION_TYPES.forEach(type => {
        userNotificationPrefs[type.key] = userPrefs[type.key] ?? true; // Habilitado por padrão
      });

      // Processar preferências da empresa
      const companyNotificationPrefs = {};
      NOTIFICATION_TYPES.forEach(type => {
        companyNotificationPrefs[type.key] = {
          enabled: companyPrefs.enabled?.[type.key] ?? true,
          required: companyPrefs.required?.[type.key] ?? false
        };
      });

      console.log('Preferências processadas:', {
        userNotificationPrefs,
        companyNotificationPrefs,
        userPerms
      });

      setPreferences(userNotificationPrefs);
      setCompanyPreferences(companyNotificationPrefs);
      setUserPermissions(userPerms);
    } catch (error) {
      console.error('Erro ao carregar preferências de notificação:', error);
      console.error('Detalhes do erro:', error.response?.data);
      console.error('Status do erro:', error.response?.status);

      // Verificar se é erro 400 (Bad Request)
      if (error.response?.status === 400) {
        console.error('Erro 400 - Possível problema com companyId do usuário');
        toast_error('Erro: ID da empresa não encontrado. Verifique se você está logado corretamente.');
      } else {
        toast_error('Erro ao carregar preferências de notificação');
      }

      // Definir valores padrão em caso de erro
      const defaultPrefs = {};
      const defaultCompanyPrefs = {};
      NOTIFICATION_TYPES.forEach(type => {
        defaultPrefs[type.key] = false;
        defaultCompanyPrefs[type.key] = {
          enabled: true,
          required: false
        };
      });
      setPreferences(defaultPrefs);
      setCompanyPreferences(defaultCompanyPrefs);
    } finally {
      setIsLoading(false);
    }
  };

  // Manipular mudanças nas preferências
  const handlePreferenceChange = async (key, value) => {
    // Atualizar o estado local imediatamente para feedback visual
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }));

    try {
      // Salvar no backend
      await api.put('/notifications/user-preferences', {
        [key]: value
      });

      const notificationType = NOTIFICATION_TYPES.find(type => type.key === key);
      toast_success(`Preferência de "${notificationType?.label}" atualizada com sucesso!`);
    } catch (error) {
      console.error(`Erro ao atualizar preferência ${key}:`, error);
      toast_error(`Erro ao salvar preferência de "${NOTIFICATION_TYPES.find(type => type.key === key)?.label}"`);
      
      // Reverter mudança em caso de erro
      setPreferences(prev => ({
        ...prev,
        [key]: !value
      }));
    }
  };

  // Verificar se uma notificação está disponível para o usuário
  const isNotificationAvailable = (key) => {
    return companyPreferences[key]?.enabled ?? true;
  };

  // Verificar se uma notificação é obrigatória
  const isNotificationRequired = (key) => {
    return companyPreferences[key]?.required ?? false;
  };

  // Verificar se o usuário tem permissão para receber uma notificação
  const hasPermissionToReceive = (key) => {
    return userPermissions[key]?.canReceive ?? false;
  };

  // Verificar se o usuário pode modificar uma preferência
  const canModifyPreference = (key) => {
    return userPermissions[key]?.canModify ?? false;
  };

  return (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-cyan-200 dark:border-cyan-700 shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center">
          <Bell size={20} className="mr-2 text-cyan-500 dark:text-cyan-400" />
          Preferências de Notificação
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
          Configure quais notificações você deseja receber. Algumas notificações podem ser obrigatórias conforme definido pelo administrador.
        </p>

        <div className="space-y-4">
          {NOTIFICATION_TYPES.map((notification) => {
            const available = isNotificationAvailable(notification.key);
            const required = isNotificationRequired(notification.key);
            const hasPermission = hasPermissionToReceive(notification.key);
            const canModify = canModifyPreference(notification.key);
            const checked = preferences[notification.key] || false;
            
            // Se a notificação não está disponível na empresa ou usuário não tem permissão, não mostrar
            if (!available || !hasPermission) {
              return null;
            }

            return (
              <div 
                key={notification.key}
                className={`p-4 rounded-lg border transition-colors ${
                  required
                    ? 'bg-cyan-50 dark:bg-cyan-900/20 border-cyan-200 dark:border-cyan-800'
                    : 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3">
                    <div className={`p-2 rounded-lg ${
                      required
                        ? 'bg-cyan-100 dark:bg-cyan-900 text-cyan-600 dark:text-cyan-400'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                    }`}>
                      {notification.icon}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {notification.label}
                        </h4>
                        {required && (
                          <span className="px-2 py-1 text-xs font-medium bg-cyan-100 dark:bg-cyan-900 text-cyan-700 dark:text-cyan-300 rounded">
                            Obrigatória
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {notification.description}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <ModuleCheckbox
                      id={`notification-${notification.key}`}
                      checked={checked}
                      onChange={(e) => handlePreferenceChange(notification.key, e.target.checked)}
                      disabled={isLoading || required || !canModify}
                    />
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        <div className="mt-6 p-4 bg-cyan-50 dark:bg-cyan-900/20 rounded-lg border border-cyan-200 dark:border-cyan-800">
          <div className="flex items-start">
            <Info size={18} className="text-cyan-500 dark:text-cyan-400 mt-0.5 mr-3 flex-shrink-0" />
            <div>
              <h4 className="text-sm font-medium text-cyan-700 dark:text-cyan-300 mb-1">
                Sobre as notificações
              </h4>
              <ul className="text-xs text-cyan-600 dark:text-cyan-400 space-y-1">
                <li>• Notificações <strong>obrigatórias</strong> não podem ser desativadas</li>
                <li>• Você só recebe notificações relacionadas às suas atividades</li>
                <li>• Algumas notificações podem não estar disponíveis conforme configuração da empresa</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserNotificationPreferences;
